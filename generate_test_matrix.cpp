#include <iostream>
#include <fstream>
#include <vector>
#include <random>
#include <set>

// 生成测试用的稀疏矩阵 (Matrix Market格式)
void generate_sparse_matrix(const std::string& filename, int M, int N, double sparsity) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> row_dis(1, M);
    std::uniform_int_distribution<> col_dis(1, N);
    
    int target_nnz = static_cast<int>(M * N * sparsity);
    std::set<std::pair<int, int>> unique_entries;
    
    // 生成唯一的非零元素位置
    while (unique_entries.size() < target_nnz) {
        int row = row_dis(gen);
        int col = col_dis(gen);
        unique_entries.insert({row, col});
    }
    
    // 写入Matrix Market格式文件
    std::ofstream file(filename);
    file << "%%MatrixMarket matrix coordinate real general\n";
    file << "% Generated test matrix\n";
    file << M << " " << N << " " << unique_entries.size() << "\n";
    
    for (const auto& entry : unique_entries) {
        file << entry.first << " " << entry.second << " 1.0\n";
    }
    
    file.close();
    std::cout << "生成测试矩阵: " << filename << std::endl;
    std::cout << "维度: " << M << "x" << N << ", 非零元素: " << unique_entries.size() 
              << " (稀疏度: " << (100.0 * unique_entries.size() / (M * N)) << "%)" << std::endl;
}

int main(int argc, char** argv) {
    if (argc < 4) {
        std::cout << "用法: " << argv[0] << " <输出文件名> <行数> <列数> [稀疏度=0.01]" << std::endl;
        std::cout << "示例: " << argv[0] << " test_matrix.mtx 1000 1000 0.05" << std::endl;
        return 1;
    }
    
    std::string filename = argv[1];
    int M = std::atoi(argv[2]);
    int N = std::atoi(argv[3]);
    double sparsity = (argc > 4) ? std::atof(argv[4]) : 0.01;
    
    if (M <= 0 || N <= 0 || sparsity <= 0 || sparsity > 1) {
        std::cerr << "错误: 无效的参数" << std::endl;
        return 1;
    }
    
    generate_sparse_matrix(filename, M, N, sparsity);
    return 0;
}
