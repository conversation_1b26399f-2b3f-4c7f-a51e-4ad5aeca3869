#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <ctime>
#include <cstdlib>
#include <limits>
#include <iomanip>
#include <cuda_runtime.h>
#include <cassert>

// Thrust 头文件
#include <thrust/device_ptr.h>
#include <thrust/scan.h>
#include <thrust/execution_policy.h>

// =================================================================
// 常量定义
// =================================================================
const int MEDIUM_DENSITY_THRESHOLD = 32;
const int HIGH_DENSITY_THRESHOLD = 256;
const int CHUNK_SIZE = 256;

// 验证过的最佳参数
const int LOW_DENSITY_BLOCK_SIZE = 256;
const int MEDIUM_DENSITY_BLOCK_SIZE = 1024;
const int CHUNK_PER_BLOCK_KERNEL_SIZE = 1024;
const int WARP_SIZE = 32;

#define CUDA_CHECK(err) { \
    cudaError_t e = err; \
    if (e != cudaSuccess) { \
        printf("Cuda error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
        exit(EXIT_FAILURE); \
    } \
}

struct CSRMatrix {
    int *row_ptr, *col_idx;
    float *values;
    int rows, cols, nnz;
};

// =================================================================
// 简单高效的Warp Shuffle优化 - 只优化点积计算部分
// =================================================================

// 简单的warp shuffle归约
__device__ __forceinline__ float warp_reduce_sum(float val) {
    for (int offset = 16; offset > 0; offset /= 2) {
        val += __shfl_down_sync(0xFFFFFFFF, val, offset);
    }
    return val;
}

// 低密度区核函数 - 在原始逻辑基础上只优化点积计算
__global__ __launch_bounds__(LOW_DENSITY_BLOCK_SIZE, 1)
void sddmm_low_density_simple_warp_shuffle_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {
    
    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;
    const int global_warp_id = blockIdx.x * warps_per_block + warp_id;
    if (global_warp_id >= num_rows) return;
    
    extern __shared__ float s_A[];
    float *a_row_s = &s_A[warp_id * K];
    const int row = row_indices[global_warp_id];
    
    // 协作加载A行到共享内存 (保持原始逻辑)
    for (int k = lane_id; k < K; k += 32) { 
        a_row_s[k] = A[(size_t) row * K + k]; 
    }
    __syncthreads();
    
    const int row_start = row_ptr[row];
    const int row_end = row_ptr[row + 1];
    
    // 处理每个非零元素 (保持原始逻辑)
    for (int idx = row_start + lane_id; idx < row_end; idx += 32) {
        const int col = col_idx[idx];
        
        // 使用warp shuffle优化点积计算
        float sum = 0.0f;
        
        // 每个线程计算K/32个元素，然后用warp shuffle归约
        for (int k = lane_id; k < K; k += 32) {
            sum += a_row_s[k] * B[(size_t) col * K + k];
        }
        
        // warp内归约求和 - 关键优化点！
        sum = warp_reduce_sum(sum);
        
        // 只有lane 0写结果
        if (lane_id == 0) {
            // 但是要写到正确的位置
            for (int i = 0; i < 32 && row_start + warp_id * 32 + i < row_end; i++) {
                if (row_start + warp_id * 32 + i == idx) {
                    result[idx] = sum;
                    break;
                }
            }
        }
        
        // 等等，这个逻辑还是有问题...让我简化
    }
}

// 让我重新实现一个更简单正确的版本
__global__ __launch_bounds__(LOW_DENSITY_BLOCK_SIZE, 1)
void sddmm_low_density_correct_warp_shuffle_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {
    
    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;
    const int global_warp_id = blockIdx.x * warps_per_block + warp_id;
    if (global_warp_id >= num_rows) return;
    
    extern __shared__ float s_A[];
    float *a_row_s = &s_A[warp_id * K];
    const int row = row_indices[global_warp_id];
    
    // 协作加载A行到共享内存 (完全保持原始逻辑)
    for (int k = lane_id; k < K; k += 32) { 
        a_row_s[k] = A[(size_t) row * K + k]; 
    }
    __syncthreads();
    
    const int row_start = row_ptr[row];
    const int row_end = row_ptr[row + 1];
    
    // 处理每个非零元素 (完全保持原始逻辑)
    for (int idx = row_start + lane_id; idx < row_end; idx += 32) {
        const int col = col_idx[idx];
        
        // 方法1: 简单直接的warp shuffle优化
        // 每个线程计算部分点积，然后归约
        float partial_sum = 0.0f;
        for (int k = lane_id; k < K; k += 32) {
            partial_sum += a_row_s[k] * B[(size_t) col * K + k];
        }
        
        // warp内归约
        partial_sum = warp_reduce_sum(partial_sum);
        
        // 所有线程都得到完整的sum
        float sum = __shfl_sync(0xFFFFFFFF, partial_sum, 0);
        
        // 每个线程写自己的结果
        result[idx] = sum;
    }
}

// 预处理与分类核函数 (保持原始)
__global__ void get_chunk_counts_per_row_kernel(
    const int *row_ptr, const int *d_high_rows, int *d_chunk_counts_per_row, int num_high_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_high_rows) return;
    int row = d_high_rows[idx];
    int nnz_in_row = row_ptr[row + 1] - row_ptr[row];
    d_chunk_counts_per_row[idx] = (nnz_in_row + CHUNK_SIZE - 1) / CHUNK_SIZE;
}

__global__ void populate_chunks_kernel(
    const int *d_high_rows, const int *d_chunk_counts_per_row, const int *d_chunk_write_offsets,
    int *d_chunk_rows_high, int *d_chunk_offsets_high, int num_high_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_high_rows) return;
    int row = d_high_rows[idx];
    int chunk_count = d_chunk_counts_per_row[idx];
    int write_offset = d_chunk_write_offsets[idx];
    for (int c = 0; c < chunk_count; c++) {
        d_chunk_rows_high[write_offset + c] = row;
        d_chunk_offsets_high[write_offset + c] = c * CHUNK_SIZE;
    }
}

__global__ void classify_rows_kernel(
    const int *row_ptr, int *d_low_rows, int *d_medium_rows, int *d_high_rows,
    int *d_low_count, int *d_medium_count, int *d_high_count, int rows) {
    int row = blockIdx.x * blockDim.x + threadIdx.x;
    if (row >= rows) return;
    int nnz_in_row = row_ptr[row + 1] - row_ptr[row];
    if (nnz_in_row <= MEDIUM_DENSITY_THRESHOLD) {
        int pos = atomicAdd(d_low_count, 1);
        d_low_rows[pos] = row;
    } else if (nnz_in_row <= HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(d_medium_count, 1);
        d_medium_rows[pos] = row;
    } else {
        int pos = atomicAdd(d_high_count, 1);
        d_high_rows[pos] = row;
    }
}

// 原始低密度区核函数 (经过验证的正确版本)
__global__ __launch_bounds__(LOW_DENSITY_BLOCK_SIZE, 1)
void sddmm_low_density_final_kernel_fixed(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {
    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;
    const int global_warp_id = blockIdx.x * warps_per_block + warp_id;
    if (global_warp_id >= num_rows) return;
    extern __shared__ float s_A[];
    float *a_row_s = &s_A[warp_id * K];
    const int row = row_indices[global_warp_id];
    for (int k = lane_id; k < K; k += 32) { a_row_s[k] = A[(size_t) row * K + k]; }
    __syncthreads();
    const int row_start = row_ptr[row];
    const int row_end = row_ptr[row + 1];
    for (int idx = row_start + lane_id; idx < row_end; idx += 32) {
        const int col = col_idx[idx];
        float sum = 0.0f;
        for (int k = 0; k < K; ++k) { sum += a_row_s[k] * B[(size_t) col * K + k]; }
        result[idx] = sum;
    }
}

// 中密度区核函数 - 简单warp shuffle优化
__global__ __launch_bounds__(MEDIUM_DENSITY_BLOCK_SIZE, 1)
void sddmm_medium_density_simple_warp_shuffle_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_rows, float *__restrict__ result,
    int num_rows, int K) {
    
    int row_idx = blockIdx.x;
    if (row_idx >= num_rows) return;

    extern __shared__ float a_row_s[];
    int row = d_rows[row_idx];

    // 协作加载A行到共享内存 (保持原始逻辑)
    for (int k = threadIdx.x + threadIdx.y * blockDim.x; k < K; k += blockDim.x * blockDim.y) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    int row_start = row_ptr[row];
    int row_end = row_ptr[row + 1];
    int nnz_in_row = row_end - row_start;

    // 处理每个非零元素 (保持原始逻辑，只优化点积计算)
    for (int idx = threadIdx.x + threadIdx.y * blockDim.x; idx < nnz_in_row; 
         idx += blockDim.x * blockDim.y) {
        int global_idx = row_start + idx;
        int col = col_idx[global_idx];
        
        // 获取当前线程的warp信息
        int thread_id = threadIdx.x + threadIdx.y * blockDim.x;
        int lane_id = thread_id % 32;
        
        // 使用warp shuffle优化点积计算
        float partial_sum = 0.0f;
        for (int k = lane_id; k < K; k += 32) {
            partial_sum += a_row_s[k] * B[(size_t) col * K + k];
        }
        
        // warp内归约
        partial_sum = warp_reduce_sum(partial_sum);
        
        // 广播给warp内所有线程
        float sum = __shfl_sync(0xFFFFFFFF, partial_sum, 0);
        
        result[global_idx] = sum;
    }
}
