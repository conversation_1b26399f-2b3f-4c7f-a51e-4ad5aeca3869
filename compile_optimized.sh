#!/bin/bash

# SDDMM 优化版本编译和运行脚本

echo "=== SDDMM 优化版本编译脚本 ==="

# 检查CUDA是否安装
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到nvcc编译器，请确保CUDA已正确安装"
    exit 1
fi

# 获取GPU架构
GPU_ARCH="sm_75"  # 默认为Turing架构
echo "检测GPU架构..."

# 尝试自动检测GPU架构
if command -v nvidia-smi &> /dev/null; then
    GPU_NAME=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)
    echo "检测到GPU: $GPU_NAME"
    
    # 根据GPU名称设置架构
    if [[ $GPU_NAME == *"RTX 40"* ]] || [[ $GPU_NAME == *"RTX 4"* ]]; then
        GPU_ARCH="sm_89"  # Ada Lovelace
        echo "设置架构为: Ada Lovelace (sm_89)"
    elif [[ $GPU_NAME == *"RTX 30"* ]] || [[ $GPU_NAME == *"RTX 3"* ]] || [[ $GPU_NAME == *"A100"* ]]; then
        GPU_ARCH="sm_86"  # Ampere
        echo "设置架构为: Ampere (sm_86)"
    elif [[ $GPU_NAME == *"RTX 20"* ]] || [[ $GPU_NAME == *"RTX 2"* ]] || [[ $GPU_NAME == *"GTX 16"* ]]; then
        GPU_ARCH="sm_75"  # Turing
        echo "设置架构为: Turing (sm_75)"
    elif [[ $GPU_NAME == *"GTX 10"* ]] || [[ $GPU_NAME == *"GTX 1"* ]]; then
        GPU_ARCH="sm_61"  # Pascal
        echo "设置架构为: Pascal (sm_61)"
    else
        echo "使用默认架构: Turing (sm_75)"
    fi
else
    echo "无法检测GPU，使用默认架构: sm_75"
fi

# 编译选项
NVCC_FLAGS="-O3 -arch=$GPU_ARCH -std=c++14 -Xcompiler -fopenmp"
LIBRARIES="-lcudart"

echo ""
echo "=== 开始编译 ==="
echo "编译命令: nvcc $NVCC_FLAGS sddmm_optimized_complete.cu $LIBRARIES -o sddmm_optimized"

# 编译
nvcc $NVCC_FLAGS sddmm_optimized_complete.cu $LIBRARIES -o sddmm_optimized

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo ""
    
    # 检查是否提供了矩阵文件参数
    if [ $# -eq 0 ]; then
        echo "=== 使用说明 ==="
        echo "运行方式: ./sddmm_optimized <matrix_file.mtx> [K]"
        echo ""
        echo "参数说明:"
        echo "  matrix_file.mtx  - Matrix Market格式的稀疏矩阵文件"
        echo "  K               - 可选，密集矩阵的列数/行数 (默认: 128)"
        echo ""
        echo "示例:"
        echo "  ./sddmm_optimized matrix.mtx 128"
        echo "  ./sddmm_optimized large_matrix.mtx 256"
        echo ""
        echo "如果您没有测试矩阵，可以从以下网站下载:"
        echo "  - https://sparse.tamu.edu/ (SuiteSparse Matrix Collection)"
        echo "  - https://www.cise.ufl.edu/research/sparse/matrices/"
    else
        echo "=== 运行程序 ==="
        echo "运行命令: ./sddmm_optimized $@"
        echo ""
        ./sddmm_optimized "$@"
    fi
else
    echo "❌ 编译失败！"
    echo ""
    echo "可能的解决方案:"
    echo "1. 检查CUDA版本是否兼容 (建议CUDA 11.0+)"
    echo "2. 检查GPU架构设置是否正确"
    echo "3. 确保安装了必要的开发库"
    echo ""
    echo "手动编译命令:"
    echo "nvcc -O3 -arch=sm_75 -std=c++14 -Xcompiler -fopenmp sddmm_optimized_complete.cu -lcudart -o sddmm_optimized"
    exit 1
fi
