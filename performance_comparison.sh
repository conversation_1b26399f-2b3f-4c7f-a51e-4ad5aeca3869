#!/bin/bash

echo "=========================================="
echo "SDDMM 性能对比测试"
echo "=========================================="

# 检查参数
if [ $# -lt 1 ]; then
    echo "用法: $0 <matrix_file.mtx> [K=128]"
    echo "示例: $0 /path/to/matrix.mtx 128"
    exit 1
fi

MATRIX_FILE=$1
K=${2:-128}

echo "测试矩阵: $MATRIX_FILE"
echo "K值: $K"
echo ""

# 检查矩阵文件是否存在
if [ ! -f "$MATRIX_FILE" ]; then
    echo "错误: 矩阵文件不存在: $MATRIX_FILE"
    exit 1
fi

# 检查可执行文件
ORIGINAL_EXEC=""
TURBO_EXEC=""

# 查找原始版本
if [ -f "sddmm_46" ]; then
    ORIGINAL_EXEC="sddmm_46"
elif [ -f "cmake-build-debug/CUDAProjects" ]; then
    ORIGINAL_EXEC="cmake-build-debug/CUDAProjects"
else
    echo "警告: 未找到原始版本可执行文件"
fi

# 查找涡轮版本
if [ -f "sddmm_turbo" ]; then
    TURBO_EXEC="sddmm_turbo"
else
    echo "错误: 未找到涡轮增压版本可执行文件"
    echo "请先运行: chmod +x compile_turbo.sh && ./compile_turbo.sh"
    exit 1
fi

echo "找到可执行文件:"
if [ -n "$ORIGINAL_EXEC" ]; then
    echo "  原始版本: $ORIGINAL_EXEC"
fi
echo "  涡轮版本: $TURBO_EXEC"
echo ""

# 创建结果目录
mkdir -p comparison_results
RESULT_FILE="comparison_results/performance_comparison_$(date +%Y%m%d_%H%M%S).txt"

echo "性能对比测试结果" > $RESULT_FILE
echo "测试时间: $(date)" >> $RESULT_FILE
echo "矩阵文件: $MATRIX_FILE" >> $RESULT_FILE
echo "K值: $K" >> $RESULT_FILE
echo "========================================" >> $RESULT_FILE

# 获取GPU信息
echo "GPU信息:" >> $RESULT_FILE
nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader >> $RESULT_FILE
echo "" >> $RESULT_FILE

# 运行原始版本
if [ -n "$ORIGINAL_EXEC" ]; then
    echo "=== 测试原始版本 ==="
    echo "原始版本测试:" >> $RESULT_FILE
    echo "----------------------------------------" >> $RESULT_FILE
    
    echo "运行命令: ./$ORIGINAL_EXEC $MATRIX_FILE $K"
    ./$ORIGINAL_EXEC "$MATRIX_FILE" "$K" >> $RESULT_FILE 2>&1
    
    echo "" >> $RESULT_FILE
    echo "========================================" >> $RESULT_FILE
    echo ""
fi

# 运行涡轮增压版本
echo "=== 测试涡轮增压版本 ==="
echo "涡轮增压版本测试:" >> $RESULT_FILE
echo "----------------------------------------" >> $RESULT_FILE

echo "运行命令: ./$TURBO_EXEC $MATRIX_FILE $K"
./$TURBO_EXEC "$MATRIX_FILE" "$K" >> $RESULT_FILE 2>&1

echo "" >> $RESULT_FILE

echo ""
echo "=== 测试完成 ==="
echo "详细结果保存在: $RESULT_FILE"

# 提取性能数据并生成摘要
echo ""
echo "=== 性能摘要 ==="

# 提取原始版本性能
if [ -n "$ORIGINAL_EXEC" ]; then
    ORIGINAL_TIME=$(grep "最佳总时间:" $RESULT_FILE | head -1 | sed 's/.*: \([0-9.]*\) ms.*/\1/')
    ORIGINAL_GFLOPS=$(grep "峰值性能:" $RESULT_FILE | head -1 | sed 's/.*: \([0-9.]*\) GFLOPS.*/\1/')
    
    if [ -n "$ORIGINAL_TIME" ] && [ -n "$ORIGINAL_GFLOPS" ]; then
        echo "原始版本:"
        echo "  最佳时间: ${ORIGINAL_TIME} ms"
        echo "  峰值性能: ${ORIGINAL_GFLOPS} GFLOPS"
    fi
fi

# 提取涡轮版本性能
TURBO_TIME=$(grep "最佳总时间:" $RESULT_FILE | tail -1 | sed 's/.*: \([0-9.]*\) ms.*/\1/')
TURBO_GFLOPS=$(grep "峰值性能:" $RESULT_FILE | tail -1 | sed 's/.*: \([0-9.]*\) GFLOPS.*/\1/')

if [ -n "$TURBO_TIME" ] && [ -n "$TURBO_GFLOPS" ]; then
    echo "涡轮版本:"
    echo "  最佳时间: ${TURBO_TIME} ms"
    echo "  峰值性能: ${TURBO_GFLOPS} GFLOPS"
fi

# 计算性能提升
if [ -n "$ORIGINAL_TIME" ] && [ -n "$TURBO_TIME" ]; then
    SPEEDUP=$(echo "scale=2; $ORIGINAL_TIME / $TURBO_TIME" | bc -l 2>/dev/null)
    IMPROVEMENT=$(echo "scale=1; ($ORIGINAL_TIME - $TURBO_TIME) / $ORIGINAL_TIME * 100" | bc -l 2>/dev/null)
    
    if [ -n "$SPEEDUP" ] && [ -n "$IMPROVEMENT" ]; then
        echo ""
        echo "性能提升:"
        echo "  加速比: ${SPEEDUP}x"
        if (( $(echo "$IMPROVEMENT > 0" | bc -l) )); then
            echo "  性能提升: +${IMPROVEMENT}%"
        else
            echo "  性能变化: ${IMPROVEMENT}%"
        fi
    fi
fi

echo ""
echo "完整结果请查看: $RESULT_FILE"
