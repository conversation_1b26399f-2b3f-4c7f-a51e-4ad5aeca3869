#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <ctime>
#include <cstdlib>
#include <limits>
#include <iomanip>
#include <cuda_runtime.h>
#include <cassert>
#include <cooperative_groups.h>

// Thrust 头文件
#include <thrust/device_ptr.h>
#include <thrust/scan.h>
#include <thrust/execution_policy.h>
#include <thrust/sort.h>
#include <thrust/sequence.h>

// =================================================================
// 优化常量定义
// =================================================================
const int MEDIUM_DENSITY_THRESHOLD = 32;
const int HIGH_DENSITY_THRESHOLD = 256;
const int ULTRA_HIGH_DENSITY_THRESHOLD = 1024;
const int CHUNK_SIZE = 256;

// 优化参数
const int LOW_DENSITY_BLOCK_SIZE = 256;
const int MEDIUM_DENSITY_BLOCK_SIZE = 1024;
const int HIGH_DENSITY_BLOCK_SIZE = 1024;
const int WARP_SIZE = 32;

// 新优化策略参数
const int VECTORIZED_THRESHOLD = 64;  // K维度阈值，使用向量化
const int DYNAMIC_LOAD_BALANCE_THRESHOLD = 5000;  // 动态负载均衡阈值
const int MULTI_STREAM_THRESHOLD = 20000;  // 多流并行阈值
const int PREFETCH_DISTANCE = 2;  // 预取距离

#define CUDA_CHECK(err) { \
    cudaError_t e = err; \
    if (e != cudaSuccess) { \
        printf("Cuda error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
        exit(EXIT_FAILURE); \
    } \
}

struct CSRMatrix {
    int *row_ptr, *col_idx;
    float *values;
    int rows, cols, nnz;
};

// 性能分析结构
struct PerformanceProfile {
    double avg_nnz_per_row;
    double nnz_variance;
    int max_nnz_per_row;
    double load_imbalance_factor;
    bool is_regular_pattern;
    
    void analyze(const std::vector<int>& row_ptr) {
        int M = row_ptr.size() - 1;
        std::vector<int> nnz_per_row(M);
        
        for (int i = 0; i < M; ++i) {
            nnz_per_row[i] = row_ptr[i + 1] - row_ptr[i];
        }
        
        avg_nnz_per_row = std::accumulate(nnz_per_row.begin(), nnz_per_row.end(), 0.0) / M;
        max_nnz_per_row = *std::max_element(nnz_per_row.begin(), nnz_per_row.end());
        
        // 计算方差
        double sum_sq_diff = 0.0;
        for (int nnz : nnz_per_row) {
            sum_sq_diff += (nnz - avg_nnz_per_row) * (nnz - avg_nnz_per_row);
        }
        nnz_variance = sum_sq_diff / M;
        
        // 负载不均衡因子
        load_imbalance_factor = max_nnz_per_row / avg_nnz_per_row;
        
        // 规律性检测
        is_regular_pattern = (nnz_variance / (avg_nnz_per_row * avg_nnz_per_row)) < 0.2;
    }
};

// 优化策略枚举
enum class OptimizationStrategy {
    VECTORIZED_COMPUTE,
    DYNAMIC_LOAD_BALANCING,
    MULTI_STREAM_PARALLEL,
    MEMORY_COALESCING,
    ORIGINAL_OPTIMIZED
};

// =================================================================
// 策略1: 向量化计算优化
// =================================================================

// 向量化内积计算内核 (使用float4)
__global__ __launch_bounds__(1024, 1)
void sddmm_vectorized_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {
    
    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;
    const int global_warp_id = blockIdx.x * warps_per_block + warp_id;
    
    if (global_warp_id >= num_rows) return;
    
    extern __shared__ float s_A[];
    float *a_row_s = &s_A[warp_id * K];
    
    const int row = row_indices[global_warp_id];
    
    // 向量化加载A矩阵行
    if (K % 4 == 0) {
        const float4 *A_vec = reinterpret_cast<const float4*>(&A[row * K]);
        float4 *a_row_vec = reinterpret_cast<float4*>(a_row_s);
        
        for (int k4 = lane_id; k4 < K / 4; k4 += 32) {
            a_row_vec[k4] = A_vec[k4];
        }
    } else {
        for (int k = lane_id; k < K; k += 32) {
            a_row_s[k] = A[row * K + k];
        }
    }
    __syncwarp();
    
    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;
    
    // 处理每个非零元素
    for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
        const int global_idx = row_start + nnz_idx;
        const int col = col_idx[global_idx];
        
        float partial_sum = 0.0f;
        
        // 向量化内积计算
        if (K % 4 == 0) {
            const float4 *B_vec = reinterpret_cast<const float4*>(&B[col * K]);
            const float4 *a_row_vec = reinterpret_cast<const float4*>(a_row_s);
            
            #pragma unroll 4
            for (int k4 = lane_id; k4 < K / 4; k4 += 32) {
                float4 a_vec = a_row_vec[k4];
                float4 b_vec = B_vec[k4];
                
                partial_sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y + 
                              a_vec.z * b_vec.z + a_vec.w * b_vec.w;
            }
        } else {
            #pragma unroll 8
            for (int k = lane_id; k < K; k += 32) {
                partial_sum += a_row_s[k] * B[col * K + k];
            }
        }
        
        // Warp reduce
        #pragma unroll
        for (int offset = 16; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }
        
        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// =================================================================
// 策略2: 动态负载均衡
// =================================================================

struct WorkItem {
    int row;
    int start_nnz;
    int nnz_count;
    int work_size;
};

__global__ void sddmm_dynamic_load_balancing_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const WorkItem *__restrict__ work_items, float *__restrict__ result,
    int total_work_items, int K) {
    
    extern __shared__ float shared_mem[];
    float *a_cache = shared_mem;
    
    const int tid = threadIdx.x;
    const int bid = blockIdx.x;
    const int block_size = blockDim.x;
    const int warp_id = tid / 32;
    const int lane_id = tid % 32;
    
    // 每个block处理多个工作项
    for (int work_idx = bid; work_idx < total_work_items; work_idx += gridDim.x) {
        WorkItem work = work_items[work_idx];
        int row = work.row;
        
        // 协作加载A矩阵行
        for (int k = tid; k < K; k += block_size) {
            a_cache[k] = A[row * K + k];
        }
        __syncthreads();
        
        // 每个warp处理部分非零元素
        int nnz_per_warp = (work.nnz_count + blockDim.x / 32 - 1) / (blockDim.x / 32);
        int warp_start = warp_id * nnz_per_warp;
        int warp_end = min(warp_start + nnz_per_warp, work.nnz_count);
        
        for (int local_nnz = warp_start; local_nnz < warp_end; ++local_nnz) {
            int global_nnz_idx = work.start_nnz + local_nnz;
            int col = col_idx[global_nnz_idx];
            
            float sum = 0.0f;
            #pragma unroll 4
            for (int k = lane_id; k < K; k += 32) {
                sum += a_cache[k] * B[col * K + k];
            }
            
            // Warp reduce
            #pragma unroll
            for (int offset = 16; offset > 0; offset /= 2) {
                sum += __shfl_down_sync(0xFFFFFFFF, sum, offset);
            }
            
            if (lane_id == 0) {
                result[global_nnz_idx] = sum;
            }
        }
        __syncthreads();
    }
}

// =================================================================
// 策略3: 内存合并访问优化
// =================================================================

__global__ __launch_bounds__(1024, 1)
void sddmm_memory_coalescing_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ sorted_rows, float *__restrict__ result,
    int num_rows, int K) {

    extern __shared__ float shared_data[];
    float *a_shared = shared_data;
    float *b_shared = shared_data + K;

    const int tid = threadIdx.x;
    const int bid = blockIdx.x;
    const int block_size = blockDim.x;

    // 每个block处理一行
    if (bid >= num_rows) return;

    int row = sorted_rows[bid];
    int row_start = row_ptr[row];
    int row_end = row_ptr[row + 1];
    int nnz_count = row_end - row_start;

    // 合并加载A矩阵行
    for (int k = tid; k < K; k += block_size) {
        a_shared[k] = A[row * K + k];
    }
    __syncthreads();

    // 分组处理非零元素以优化B矩阵访问
    const int GROUP_SIZE = 32;  // 一个warp处理的列数

    for (int group_start = 0; group_start < nnz_count; group_start += GROUP_SIZE) {
        int group_end = min(group_start + GROUP_SIZE, nnz_count);
        int group_size = group_end - group_start;

        // 预取列索引
        __shared__ int cols[GROUP_SIZE];
        if (tid < group_size) {
            cols[tid] = col_idx[row_start + group_start + tid];
        }
        __syncthreads();

        // 处理这一组的非零元素
        for (int i = 0; i < group_size; ++i) {
            int col = cols[i];
            int global_nnz_idx = row_start + group_start + i;

            // 合并加载B矩阵列
            for (int k = tid; k < K; k += block_size) {
                b_shared[k] = B[col * K + k];
            }
            __syncthreads();

            // 计算内积
            float sum = 0.0f;
            for (int k = tid; k < K; k += block_size) {
                sum += a_shared[k] * b_shared[k];
            }

            // Block reduce
            __shared__ float reduce_buffer[1024];
            reduce_buffer[tid] = sum;
            __syncthreads();

            for (int stride = block_size / 2; stride > 0; stride /= 2) {
                if (tid < stride) {
                    reduce_buffer[tid] += reduce_buffer[tid + stride];
                }
                __syncthreads();
            }

            if (tid == 0) {
                result[global_nnz_idx] = reduce_buffer[0];
            }
            __syncthreads();
        }
    }
}

// =================================================================
// 策略4: 多流并行处理
// =================================================================

class MultiStreamProcessor {
private:
    static const int NUM_STREAMS = 4;
    cudaStream_t streams[NUM_STREAMS];
    cudaEvent_t events[NUM_STREAMS];

    struct StreamData {
        int *row_indices;
        float *a_buffer;
        float *result_buffer;
        int start_row, end_row;
        int start_nnz, end_nnz;
    };

    StreamData stream_data[NUM_STREAMS];

public:
    MultiStreamProcessor() {
        for (int i = 0; i < NUM_STREAMS; ++i) {
            CUDA_CHECK(cudaStreamCreate(&streams[i]));
            CUDA_CHECK(cudaEventCreate(&events[i]));
        }
    }

    ~MultiStreamProcessor() {
        for (int i = 0; i < NUM_STREAMS; ++i) {
            cudaStreamDestroy(streams[i]);
            cudaEventDestroy(events[i]);
            if (stream_data[i].row_indices) cudaFree(stream_data[i].row_indices);
            if (stream_data[i].a_buffer) cudaFree(stream_data[i].a_buffer);
            if (stream_data[i].result_buffer) cudaFree(stream_data[i].result_buffer);
        }
    }

    void setup_streams(const std::vector<int>& row_ptr, int M, int K, int nnz) {
        // 智能分割数据
        int rows_per_stream = (M + NUM_STREAMS - 1) / NUM_STREAMS;

        for (int i = 0; i < NUM_STREAMS; ++i) {
            stream_data[i].start_row = i * rows_per_stream;
            stream_data[i].end_row = min((i + 1) * rows_per_stream, M);

            if (stream_data[i].start_row >= M) {
                stream_data[i].end_row = stream_data[i].start_row;
                continue;
            }

            stream_data[i].start_nnz = row_ptr[stream_data[i].start_row];
            stream_data[i].end_nnz = row_ptr[stream_data[i].end_row];

            int stream_rows = stream_data[i].end_row - stream_data[i].start_row;
            int stream_nnz = stream_data[i].end_nnz - stream_data[i].start_nnz;

            // 分配GPU内存
            CUDA_CHECK(cudaMalloc(&stream_data[i].row_indices, stream_rows * sizeof(int)));
            CUDA_CHECK(cudaMalloc(&stream_data[i].a_buffer, stream_rows * K * sizeof(float)));
            CUDA_CHECK(cudaMalloc(&stream_data[i].result_buffer, stream_nnz * sizeof(float)));
        }
    }

    void execute_parallel(const float* d_A, const float* d_B, const CSRMatrix& sparse, int K) {
        // 异步启动所有流
        for (int i = 0; i < NUM_STREAMS; ++i) {
            if (stream_data[i].start_row >= stream_data[i].end_row) continue;

            int stream_rows = stream_data[i].end_row - stream_data[i].start_row;

            // 生成行索引
            thrust::sequence(thrust::cuda::par.on(streams[i]),
                           thrust::device_ptr<int>(stream_data[i].row_indices),
                           thrust::device_ptr<int>(stream_data[i].row_indices + stream_rows),
                           stream_data[i].start_row);

            // 异步复制A矩阵数据
            CUDA_CHECK(cudaMemcpyAsync(stream_data[i].a_buffer,
                                     &d_A[stream_data[i].start_row * K],
                                     stream_rows * K * sizeof(float),
                                     cudaMemcpyDeviceToDevice, streams[i]));

            // 启动计算内核
            int warps_per_block = 1024 / 32;
            dim3 grid((stream_rows + warps_per_block - 1) / warps_per_block);
            dim3 block(1024);
            size_t shared_mem = warps_per_block * K * sizeof(float);

            if (shared_mem <= 48 * 1024) {
                sddmm_vectorized_kernel<<<grid, block, shared_mem, streams[i]>>>(
                    stream_data[i].a_buffer, d_B, sparse.row_ptr, sparse.col_idx,
                    stream_data[i].row_indices, stream_data[i].result_buffer, stream_rows, K);
            }

            // 异步复制结果
            CUDA_CHECK(cudaMemcpyAsync(&sparse.values[stream_data[i].start_nnz],
                                     stream_data[i].result_buffer,
                                     (stream_data[i].end_nnz - stream_data[i].start_nnz) * sizeof(float),
                                     cudaMemcpyDeviceToDevice, streams[i]));

            CUDA_CHECK(cudaEventRecord(events[i], streams[i]));
        }

        // 等待所有流完成
        for (int i = 0; i < NUM_STREAMS; ++i) {
            if (stream_data[i].start_row < stream_data[i].end_row) {
                CUDA_CHECK(cudaEventSynchronize(events[i]));
            }
        }
    }
};

// =================================================================
// 智能策略选择和执行函数
// =================================================================

OptimizationStrategy select_best_strategy(const PerformanceProfile& profile, int K, int nnz) {
    printf("\n=== 性能分析结果 ===\n");
    printf("平均每行NNZ: %.2f\n", profile.avg_nnz_per_row);
    printf("NNZ方差: %.2f\n", profile.nnz_variance);
    printf("最大每行NNZ: %d\n", profile.max_nnz_per_row);
    printf("负载不均衡因子: %.2f\n", profile.load_imbalance_factor);
    printf("是否规律模式: %s\n", profile.is_regular_pattern ? "是" : "否");

    // 策略选择逻辑
    if (K >= VECTORIZED_THRESHOLD && K % 4 == 0) {
        printf("选择策略: 向量化计算优化 (K=%d >= %d, 且K为4的倍数)\n", K, VECTORIZED_THRESHOLD);
        return OptimizationStrategy::VECTORIZED_COMPUTE;
    }

    if (nnz > MULTI_STREAM_THRESHOLD) {
        printf("选择策略: 多流并行处理 (NNZ=%d > %d)\n", nnz, MULTI_STREAM_THRESHOLD);
        return OptimizationStrategy::MULTI_STREAM_PARALLEL;
    }

    if (profile.load_imbalance_factor > 3.0 && nnz > DYNAMIC_LOAD_BALANCE_THRESHOLD) {
        printf("选择策略: 动态负载均衡 (负载不均衡因子=%.2f)\n", profile.load_imbalance_factor);
        return OptimizationStrategy::DYNAMIC_LOAD_BALANCING;
    }

    if (profile.is_regular_pattern && profile.avg_nnz_per_row > 16) {
        printf("选择策略: 内存合并访问优化 (规律模式)\n");
        return OptimizationStrategy::MEMORY_COALESCING;
    }

    printf("选择策略: 原始优化方法 (默认)\n");
    return OptimizationStrategy::ORIGINAL_OPTIMIZED;
}

// 生成工作项
void generate_work_items(const std::vector<int>& row_ptr, std::vector<WorkItem>& work_items) {
    work_items.clear();
    int M = row_ptr.size() - 1;

    for (int row = 0; row < M; ++row) {
        int row_start = row_ptr[row];
        int row_end = row_ptr[row + 1];
        int nnz_count = row_end - row_start;

        if (nnz_count == 0) continue;

        // 自适应分块
        int chunk_size = 64;
        if (nnz_count > 512) chunk_size = 128;
        else if (nnz_count > 128) chunk_size = 64;
        else chunk_size = nnz_count;

        for (int start = row_start; start < row_end; start += chunk_size) {
            WorkItem item;
            item.row = row;
            item.start_nnz = start;
            item.nnz_count = min(chunk_size, row_end - start);
            item.work_size = item.nnz_count;
            work_items.push_back(item);
        }
    }

    // 按工作量排序
    std::sort(work_items.begin(), work_items.end(),
              [](const WorkItem& a, const WorkItem& b) {
                  return a.work_size > b.work_size;
              });
}

// 主要执行函数
void execute_optimized_sddmm(
    const float* d_A, const float* d_B, CSRMatrix& sparse,
    const std::vector<int>& h_row_ptr, int K, float& execution_time) {

    // 分析性能特征
    PerformanceProfile profile;
    profile.analyze(h_row_ptr);

    // 选择最优策略
    OptimizationStrategy strategy = select_best_strategy(profile, K, sparse.nnz);

    cudaEvent_t start, stop;
    CUDA_CHECK(cudaEventCreate(&start));
    CUDA_CHECK(cudaEventCreate(&stop));

    CUDA_CHECK(cudaEventRecord(start));

    switch (strategy) {
        case OptimizationStrategy::VECTORIZED_COMPUTE: {
            // 向量化计算
            int *d_all_rows;
            CUDA_CHECK(cudaMalloc(&d_all_rows, sparse.rows * sizeof(int)));
            thrust::sequence(thrust::device, d_all_rows, d_all_rows + sparse.rows);

            int warps_per_block = 1024 / 32;
            dim3 grid((sparse.rows + warps_per_block - 1) / warps_per_block);
            dim3 block(1024);
            size_t shared_mem = warps_per_block * K * sizeof(float);

            if (shared_mem <= 48 * 1024) {
                sddmm_vectorized_kernel<<<grid, block, shared_mem>>>(
                    d_A, d_B, sparse.row_ptr, sparse.col_idx, d_all_rows, sparse.values, sparse.rows, K);
            }

            CUDA_CHECK(cudaFree(d_all_rows));
            break;
        }

        case OptimizationStrategy::DYNAMIC_LOAD_BALANCING: {
            // 动态负载均衡
            std::vector<WorkItem> work_items;
            generate_work_items(h_row_ptr, work_items);

            WorkItem* d_work_items;
            CUDA_CHECK(cudaMalloc(&d_work_items, work_items.size() * sizeof(WorkItem)));
            CUDA_CHECK(cudaMemcpy(d_work_items, work_items.data(),
                                work_items.size() * sizeof(WorkItem), cudaMemcpyHostToDevice));

            int blocks = min(1024, (int)work_items.size() / 4 + 1);
            dim3 grid(blocks);
            dim3 block(1024);
            size_t shared_mem = K * sizeof(float);

            sddmm_dynamic_load_balancing_kernel<<<grid, block, shared_mem>>>(
                d_A, d_B, sparse.row_ptr, sparse.col_idx, d_work_items, sparse.values,
                work_items.size(), K);

            CUDA_CHECK(cudaFree(d_work_items));
            break;
        }

        case OptimizationStrategy::MULTI_STREAM_PARALLEL: {
            // 多流并行
            MultiStreamProcessor processor;
            processor.setup_streams(h_row_ptr, sparse.rows, K, sparse.nnz);
            processor.execute_parallel(d_A, d_B, sparse, K);
            break;
        }

        case OptimizationStrategy::MEMORY_COALESCING: {
            // 内存合并访问优化
            int *d_sorted_rows;
            CUDA_CHECK(cudaMalloc(&d_sorted_rows, sparse.rows * sizeof(int)));
            thrust::sequence(thrust::device, d_sorted_rows, d_sorted_rows + sparse.rows);

            dim3 grid(sparse.rows);
            dim3 block(1024);
            size_t shared_mem = 2 * K * sizeof(float);

            if (shared_mem <= 48 * 1024) {
                sddmm_memory_coalescing_kernel<<<grid, block, shared_mem>>>(
                    d_A, d_B, sparse.row_ptr, sparse.col_idx, d_sorted_rows, sparse.values, sparse.rows, K);
            }

            CUDA_CHECK(cudaFree(d_sorted_rows));
            break;
        }

        default: {
            // 原始优化方法 - 简化版本
            int *d_all_rows;
            CUDA_CHECK(cudaMalloc(&d_all_rows, sparse.rows * sizeof(int)));
            thrust::sequence(thrust::device, d_all_rows, d_all_rows + sparse.rows);

            int warps_per_block = 8;
            dim3 grid((sparse.rows + warps_per_block - 1) / warps_per_block);
            dim3 block(256);
            size_t shared_mem = warps_per_block * K * sizeof(float);

            if (shared_mem <= 48 * 1024) {
                sddmm_vectorized_kernel<<<grid, block, shared_mem>>>(
                    d_A, d_B, sparse.row_ptr, sparse.col_idx, d_all_rows, sparse.values, sparse.rows, K);
            }

            CUDA_CHECK(cudaFree(d_all_rows));
            break;
        }
    }

    CUDA_CHECK(cudaEventRecord(stop));
    CUDA_CHECK(cudaEventSynchronize(stop));
    CUDA_CHECK(cudaEventElapsedTime(&execution_time, start, stop));

    CUDA_CHECK(cudaEventDestroy(start));
    CUDA_CHECK(cudaEventDestroy(stop));
}

// =================================================================
// 辅助函数
// =================================================================

void sddmm_cpu_reference(const float *A, const float *B, const int *row_ptr, const int *col_idx,
                         float *values, int M, int N, int K) {
    #pragma omp parallel for schedule(dynamic)
    for (int row = 0; row < M; ++row) {
        int start = row_ptr[row];
        int end = row_ptr[row + 1];
        for (int idx = start; idx < end; ++idx) {
            int col = col_idx[idx];
            float sum = 0.0f;
            #pragma GCC ivdep
            for (int k = 0; k < K; ++k) {
                sum += A[row * K + k] * B[col * K + k];
            }
            values[idx] = sum;
        }
    }
}

void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
                     std::vector<int> &coo_rows, std::vector<int> &coo_cols) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "错误: 无法打开矩阵文件: " << filename << std::endl;
        exit(1);
    }

    // 跳过注释行
    while (file.peek() == '%') {
        file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    }

    file >> M >> N >> nnz;
    coo_rows.resize(nnz);
    coo_cols.resize(nnz);

    for (int i = 0; i < nnz; ++i) {
        int r, c;
        file >> r >> c;
        coo_rows[i] = r - 1;  // 转换为0-based索引
        coo_cols[i] = c - 1;
        file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    }
    file.close();
}

void coo_to_csr(int M, int nnz, const std::vector<int> &coo_rows_in, const std::vector<int> &coo_cols_in,
                std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
    csr_row_ptr.assign(M + 1, 0);

    // 创建COO对并排序
    std::vector<std::pair<int, int>> coo(nnz);
    for (int i = 0; i < nnz; ++i) {
        coo[i] = {coo_rows_in[i], coo_cols_in[i]};
    }
    std::sort(coo.begin(), coo.end());

    // 构建CSR格式
    csr_col_idx.resize(nnz);
    for (int i = 0; i < nnz; ++i) {
        csr_col_idx[i] = coo[i].second;
        csr_row_ptr[coo[i].first + 1]++;
    }

    // 累积求和得到行指针
    for (int i = 0; i < M; ++i) {
        csr_row_ptr[i + 1] += csr_row_ptr[i];
    }
}

__global__ void warmup_kernel() {
    // 预热GPU
}

// =================================================================
// 主函数
// =================================================================

int main(int argc, char **argv) {
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
        return 1;
    }

    std::srand(std::time(nullptr));
    std::string filename = argv[1];
    int K = (argc > 2) ? std::atoi(argv[2]) : 128;

    // 获取GPU信息
    int device;
    cudaGetDevice(&device);
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device);

    printf("=== GPU信息 ===\n");
    printf("设备名称: %s (Compute Capability %d.%d)\n", prop.name, prop.major, prop.minor);
    printf("设备共享内存/块: %zu bytes\n", prop.sharedMemPerBlock);
    printf("设备全局内存: %.2f GB\n", prop.totalGlobalMem / (1024.0 * 1024.0 * 1024.0));
    printf("多处理器数量: %d\n", prop.multiProcessorCount);
    printf("每个多处理器的最大线程数: %d\n\n", prop.maxThreadsPerMultiProcessor);

    // 加载矩阵
    int M, N, nnz;
    std::vector<int> coo_rows, coo_cols;
    load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);

    // 转换为CSR格式
    std::vector<int> h_csr_row_ptr, h_csr_col_idx;
    coo_to_csr(M, nnz, coo_rows, coo_cols, h_csr_row_ptr, h_csr_col_idx);

    printf("=== 矩阵信息 ===\n");
    printf("矩阵文件: %s\n", filename.c_str());
    printf("矩阵维度: M=%d, N=%d, K=%d\n", M, N, K);
    printf("非零元素: %d (稀疏度: %.4f%%)\n\n", nnz, 100.0 * nnz / ((double) M * N));

    // 生成随机矩阵A和B
    std::vector<float> h_A(M * K), h_B(N * K);
    for (int i = 0; i < M * K; ++i) h_A[i] = (rand() % 100 + 1) / 100.0f;
    for (int i = 0; i < N * K; ++i) h_B[i] = (rand() % 100 + 1) / 100.0f;

    // 分配GPU内存
    float *d_A, *d_B;
    CSRMatrix sparse;
    sparse.rows = M;
    sparse.cols = N;
    sparse.nnz = nnz;

    CUDA_CHECK(cudaMalloc(&d_A, M * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_B, N * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&sparse.col_idx, nnz * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&sparse.values, nnz * sizeof(float)));

    // 复制数据到GPU
    CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice));

    // 预热GPU
    std::cout << "预热GPU..." << std::endl;
    warmup_kernel<<<1, 1>>>();
    CUDA_CHECK(cudaDeviceSynchronize());

    printf("\n=== 开始执行优化版SDDMM ===\n");

    // 多次运行取最佳性能
    const int num_runs = 5;
    std::vector<float> execution_times;

    for (int run = 0; run < num_runs; ++run) {
        CUDA_CHECK(cudaMemset(sparse.values, 0, nnz * sizeof(float)));
        CUDA_CHECK(cudaDeviceSynchronize());

        float exec_time = 0.0f;
        execute_optimized_sddmm(d_A, d_B, sparse, h_csr_row_ptr, K, exec_time);

        execution_times.push_back(exec_time);
        printf("运行 %d: %.3f ms\n", run + 1, exec_time);
    }

    // 计算性能统计
    float min_time = *std::min_element(execution_times.begin(), execution_times.end());
    float avg_time = std::accumulate(execution_times.begin(), execution_times.end(), 0.0f) / num_runs;
    double gflops = (2.0 * (double) nnz * K) / (min_time * 1e6);

    printf("\n=== 性能统计 ===\n");
    printf("平均执行时间: %.3f ms\n", avg_time);
    printf("最佳执行时间: %.3f ms\n", min_time);
    printf("峰值性能: %.2f GFLOPS\n", gflops);

    // 验证正确性
    std::cout << "\n验证计算正确性..." << std::endl;
    std::vector<float> h_values_gpu(nnz);
    CUDA_CHECK(cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost));

    std::vector<float> h_values_cpu(nnz, 0.0f);
    auto cpu_start = std::chrono::high_resolution_clock::now();
    sddmm_cpu_reference(h_A.data(), h_B.data(), h_csr_row_ptr.data(), h_csr_col_idx.data(),
                       h_values_cpu.data(), M, N, K);
    auto cpu_end = std::chrono::high_resolution_clock::now();
    auto cpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(cpu_end - cpu_start);

    printf("CPU参考实现时间: %ld ms\n", cpu_duration.count());
    if (min_time > 0) {
        printf("GPU加速比: %.2fx\n", (float) cpu_duration.count() / min_time);
    }

    // 计算误差
    int correct_count = 0;
    float max_error = 0.0f;
    double total_abs_error = 0.0, l1_norm_cpu = 0.0;

    for (int i = 0; i < nnz; i++) {
        float abs_diff = std::abs(h_values_cpu[i] - h_values_gpu[i]);
        total_abs_error += abs_diff;
        l1_norm_cpu += std::abs(h_values_cpu[i]);
        if (abs_diff > max_error) max_error = abs_diff;

        bool is_correct = false;
        if (std::abs(h_values_cpu[i]) > 1e-9) {
            if ((abs_diff / std::abs(h_values_cpu[i])) < 1e-4) is_correct = true;
        } else {
            if (abs_diff < 1e-6) is_correct = true;
        }
        if (is_correct) correct_count++;
    }

    printf("\n=== 验证结果 ===\n");
    printf("最大绝对误差: %.2e\n", max_error);
    if (l1_norm_cpu > 0) {
        printf("相对L1误差: %.2e\n", total_abs_error / l1_norm_cpu);
    }
    printf("正确率: %.4f%%\n", (100.0f * correct_count / nnz));

    // 清理内存
    CUDA_CHECK(cudaFree(d_A));
    CUDA_CHECK(cudaFree(d_B));
    CUDA_CHECK(cudaFree(sparse.row_ptr));
    CUDA_CHECK(cudaFree(sparse.col_idx));
    CUDA_CHECK(cudaFree(sparse.values));

    std::cout << "\n程序正常结束。" << std::endl;
    return 0;
}
