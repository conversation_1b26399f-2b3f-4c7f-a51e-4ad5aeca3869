#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <ctime>
#include <cstdlib>
#include <limits>
#include <iomanip>
#include <cuda_runtime.h>
#include <cassert>

// Thrust 头文件
#include <thrust/device_ptr.h>
#include <thrust/scan.h>
#include <thrust/execution_policy.h>
#include <thrust/sort.h>

// =================================================================
// 极限优化版本 - 针对高负载不均衡的激进策略
// =================================================================

// 重新设计的分区策略
const int MEDIUM_DENSITY_THRESHOLD = 32;
const int HIGH_DENSITY_THRESHOLD = 128;  // 降低阈值，更多行进入高密度处理
const int ULTRA_HIGH_DENSITY_THRESHOLD = 512;  // 新增超高密度区
const int CHUNK_SIZE = 64;  // 大幅减小chunk size

// 极限优化参数
const int LOW_DENSITY_BLOCK_SIZE = 256;
const int MEDIUM_DENSITY_BLOCK_SIZE = 256;  // 减小以提高occupancy
const int HIGH_DENSITY_BLOCK_SIZE = 256;    // 大幅减小
const int ULTRA_HIGH_DENSITY_BLOCK_SIZE = 128;  // 超高密度区使用更小的block
const int WARP_SIZE = 32;

#define CUDA_CHECK(err) { \
    cudaError_t e = err; \
    if (e != cudaSuccess) { \
        printf("Cuda error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
        exit(EXIT_FAILURE); \
    } \
}

struct CSRMatrix {
    int *row_ptr, *col_idx;
    float *values;
    int rows, cols, nnz;
};

// =================================================================
// 四层分区策略
// =================================================================
__global__ void classify_rows_four_level_kernel(
    const int *row_ptr, int rows,
    int *d_low_rows, int *d_medium_rows, int *d_high_rows, int *d_ultra_high_rows,
    int *low_count, int *medium_count, int *high_count, int *ultra_high_count) {
    int row = blockIdx.x * blockDim.x + threadIdx.x;
    if (row >= rows) return;
    int nnz = row_ptr[row + 1] - row_ptr[row];
    if (nnz > 0 && nnz <= MEDIUM_DENSITY_THRESHOLD) {
        int pos = atomicAdd(low_count, 1);
        d_low_rows[pos] = row;
    } else if (nnz > MEDIUM_DENSITY_THRESHOLD && nnz <= HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(medium_count, 1);
        d_medium_rows[pos] = row;
    } else if (nnz > HIGH_DENSITY_THRESHOLD && nnz <= ULTRA_HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(high_count, 1);
        d_high_rows[pos] = row;
    } else if (nnz > ULTRA_HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(ultra_high_count, 1);
        d_ultra_high_rows[pos] = row;
    }
}

__global__ void get_chunk_counts_per_row_kernel(
    const int *row_ptr, const int *d_high_rows, int *d_chunk_counts_per_row, int num_high_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_high_rows) return;
    int row = d_high_rows[idx];
    int nnz_in_row = row_ptr[row + 1] - row_ptr[row];
    d_chunk_counts_per_row[idx] = (nnz_in_row + CHUNK_SIZE - 1) / CHUNK_SIZE;
}

__global__ void populate_chunks_kernel(
    const int *d_high_rows, const int *d_chunk_counts_per_row, const int *d_chunk_write_offsets,
    int *d_chunk_rows_high, int *d_chunk_offsets_high, int num_high_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_high_rows) return;
    int row = d_high_rows[idx];
    int num_chunks_for_this_row = d_chunk_counts_per_row[idx];
    int write_pos_start = d_chunk_write_offsets[idx];
    for (int i = 0; i < num_chunks_for_this_row; ++i) {
        d_chunk_rows_high[write_pos_start + i] = row;
        d_chunk_offsets_high[write_pos_start + i] = i * CHUNK_SIZE;
    }
}

// =================================================================
// 极限优化内核
// =================================================================

// 低密度区 - 保持原有优化
__global__ __launch_bounds__(LOW_DENSITY_BLOCK_SIZE, 4)
void sddmm_low_density_extreme_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {

    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;
    const int global_warp_id = blockIdx.x * warps_per_block + warp_id;

    if (global_warp_id >= num_rows) return;

    extern __shared__ float s_A[];
    float *a_row_s = &s_A[warp_id * K];

    const int row = row_indices[global_warp_id];

    // 向量化加载A矩阵行
    for (int k = lane_id; k < K; k += 32) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncwarp();

    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;

    for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
        const int global_idx = row_start + nnz_idx;
        const int col = col_idx[global_idx];

        float partial_sum = 0.0f;

        #pragma unroll 8
        for (int k = lane_id; k < K; k += 32) {
            partial_sum += a_row_s[k] * B[(size_t) col * K + k];
        }

        #pragma unroll
        for (int offset = 16; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// 中密度区 - 极限优化
__global__ __launch_bounds__(MEDIUM_DENSITY_BLOCK_SIZE, 4)
void sddmm_medium_density_extreme_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_rows, float *__restrict__ result,
    int num_rows, int K) {

    int row_idx = blockIdx.x;
    if (row_idx >= num_rows) return;

    extern __shared__ float a_row_s[];
    int row = d_rows[row_idx];

    // 高效加载A矩阵行
    for (int k = threadIdx.x; k < K; k += blockDim.x) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;

    int row_start = row_ptr[row];
    int nnz_in_row = row_ptr[row + 1] - row_start;

    // 每个warp处理多个非零元素
    for (int nnz_offset = warp_id; nnz_offset < nnz_in_row; nnz_offset += warps_per_block) {
        int global_idx = row_start + nnz_offset;
        int col = col_idx[global_idx];

        float partial_sum = 0.0f;

        #pragma unroll 4
        for (int k = lane_id; k < K; k += 32) {
            partial_sum += a_row_s[k] * B[(size_t) col * K + k];
        }

        #pragma unroll
        for (int offset = 16; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// 高密度区 - 极限优化
__global__ __launch_bounds__(HIGH_DENSITY_BLOCK_SIZE, 4)
void sddmm_high_density_extreme_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_chunk_rows,
    const int *__restrict__ d_chunk_offsets,
    float *__restrict__ result,
    int num_chunks, int K) {

    int chunk_id = blockIdx.x;
    if (chunk_id >= num_chunks) return;

    extern __shared__ float a_row_s[];
    int row = d_chunk_rows[chunk_id];

    // 协作加载A矩阵行
    for (int k = threadIdx.x; k < K; k += blockDim.x) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;

    int chunk_nnz_start_offset = d_chunk_offsets[chunk_id];
    int row_start_in_csr = row_ptr[row];
    int nnz_in_row = row_ptr[row + 1] - row_start_in_csr;
    int nnz_in_chunk = min(CHUNK_SIZE, nnz_in_row - chunk_nnz_start_offset);

    // 每个warp处理多个非零元素
    for (int nnz_offset_in_chunk = warp_id; nnz_offset_in_chunk < nnz_in_chunk;
         nnz_offset_in_chunk += warps_per_block) {
        int global_idx = row_start_in_csr + chunk_nnz_start_offset + nnz_offset_in_chunk;
        int col = col_idx[global_idx];

        float partial_sum = 0.0f;

        #pragma unroll 4
        for (int k = lane_id; k < K; k += 32) {
            partial_sum += a_row_s[k] * B[(size_t) col * K + k];
        }

        #pragma unroll
        for (int offset = 16; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// 超高密度区 - 新增的极限优化
__global__ __launch_bounds__(ULTRA_HIGH_DENSITY_BLOCK_SIZE, 8)
void sddmm_ultra_high_density_extreme_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_chunk_rows,
    const int *__restrict__ d_chunk_offsets,
    float *__restrict__ result,
    int num_chunks, int K) {

    int chunk_id = blockIdx.x;
    if (chunk_id >= num_chunks) return;

    extern __shared__ float a_row_s[];
    int row = d_chunk_rows[chunk_id];

    // 协作加载A矩阵行
    for (int k = threadIdx.x; k < K; k += blockDim.x) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;

    int chunk_nnz_start_offset = d_chunk_offsets[chunk_id];
    int row_start_in_csr = row_ptr[row];
    int nnz_in_row = row_ptr[row + 1] - row_start_in_csr;
    int nnz_in_chunk = min(CHUNK_SIZE, nnz_in_row - chunk_nnz_start_offset);

    // 更激进的并行策略
    for (int nnz_offset_in_chunk = warp_id; nnz_offset_in_chunk < nnz_in_chunk;
         nnz_offset_in_chunk += warps_per_block) {
        int global_idx = row_start_in_csr + chunk_nnz_start_offset + nnz_offset_in_chunk;
        int col = col_idx[global_idx];

        float partial_sum = 0.0f;

        // 更激进的循环展开
        #pragma unroll 8
        for (int k = lane_id; k < K; k += 32) {
            partial_sum += a_row_s[k] * B[(size_t) col * K + k];
        }

        #pragma unroll
        for (int offset = 16; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// =================================================================
// 极限优化主控函数
// =================================================================
void sddmm_extreme_execution(
    const float *d_A, const float *d_B, CSRMatrix &sparse, const std::vector<int> &h_csr_row_ptr, int K,
    size_t shared_mem_per_block,
    float &time_low, float &time_med, float &time_high, float &time_ultra_high) {

    int *d_low_rows, *d_medium_rows, *d_high_rows, *d_ultra_high_rows;
    int *d_low_count, *d_medium_count, *d_high_count, *d_ultra_high_count;

    CUDA_CHECK(cudaMalloc(&d_low_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_ultra_high_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_low_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_ultra_high_count, sizeof(int)));

    CUDA_CHECK(cudaMemset(d_low_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_medium_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_high_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_ultra_high_count, 0, sizeof(int)));

    // 使用四个流进行并行处理
    cudaStream_t streams[4];
    for (int i = 0; i < 4; ++i) CUDA_CHECK(cudaStreamCreate(&streams[i]));

    cudaEvent_t start_low, stop_low, start_med, stop_med, start_high, stop_high, start_ultra, stop_ultra;
    CUDA_CHECK(cudaEventCreate(&start_low));
    CUDA_CHECK(cudaEventCreate(&stop_low));
    CUDA_CHECK(cudaEventCreate(&start_med));
    CUDA_CHECK(cudaEventCreate(&stop_med));
    CUDA_CHECK(cudaEventCreate(&start_high));
    CUDA_CHECK(cudaEventCreate(&stop_high));
    CUDA_CHECK(cudaEventCreate(&start_ultra));
    CUDA_CHECK(cudaEventCreate(&stop_ultra));

    // 四层行分类
    dim3 block_classify(256);
    dim3 grid_classify((sparse.rows + block_classify.x - 1) / block_classify.x);
    classify_rows_four_level_kernel<<<grid_classify, block_classify, 0, streams[0]>>>(
        sparse.row_ptr, sparse.rows, d_low_rows, d_medium_rows, d_high_rows, d_ultra_high_rows,
        d_low_count, d_medium_count, d_high_count, d_ultra_high_count);

    int h_counts[4] = {0, 0, 0, 0};
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[0], d_low_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[1], d_medium_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[2], d_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[3], d_ultra_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));

    CUDA_CHECK(cudaStreamSynchronize(streams[0]));

    printf("行分类结果: 低(<=%d)=%d, 中(<=%d)=%d, 高(<=%d)=%d, 超高(>%d)=%d\n",
           MEDIUM_DENSITY_THRESHOLD, h_counts[0], HIGH_DENSITY_THRESHOLD, h_counts[1],
           ULTRA_HIGH_DENSITY_THRESHOLD, h_counts[2], ULTRA_HIGH_DENSITY_THRESHOLD, h_counts[3]);

    // 低密度区处理
    if (h_counts[0] > 0) {
        size_t required_mem_low = (size_t) (LOW_DENSITY_BLOCK_SIZE / 32) * K * sizeof(float);
        if (required_mem_low > shared_mem_per_block) {
            printf("警告: 低密度区核函数需要的共享内存超出限制。\n");
            time_low = -1.0f;
        } else {
            CUDA_CHECK(cudaEventRecord(start_low, streams[0]));
            const int warps_per_block_low = LOW_DENSITY_BLOCK_SIZE / 32;
            dim3 grid_low((h_counts[0] + warps_per_block_low - 1) / warps_per_block_low);
            dim3 block_low(LOW_DENSITY_BLOCK_SIZE);
            sddmm_low_density_extreme_kernel<<<grid_low, block_low, required_mem_low, streams[0]>>>(
                d_A, d_B, sparse.row_ptr, sparse.col_idx, d_low_rows, sparse.values, h_counts[0], K);
            CUDA_CHECK(cudaEventRecord(stop_low, streams[0]));
        }
    }

    // 中密度区处理
    if (h_counts[1] > 0) {
        size_t required_mem_medium = (size_t) K * sizeof(float);
        CUDA_CHECK(cudaEventRecord(start_med, streams[1]));
        dim3 grid_med(h_counts[1]);
        dim3 block_med(MEDIUM_DENSITY_BLOCK_SIZE);
        sddmm_medium_density_extreme_kernel<<<grid_med, block_med, required_mem_medium, streams[1]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx, d_medium_rows, sparse.values, h_counts[1], K);
        CUDA_CHECK(cudaEventRecord(stop_med, streams[1]));
    }

    // 高密度区处理
    if (h_counts[2] > 0) {
        size_t required_mem_high = (size_t) K * sizeof(float);
        std::vector<int> h_high_rows(h_counts[2]);
        CUDA_CHECK(cudaMemcpy(h_high_rows.data(), d_high_rows, h_counts[2] * sizeof(int), cudaMemcpyDeviceToHost));
        int num_chunks = 0;
        for (int row_idx: h_high_rows) {
            int nnz_in_row = h_csr_row_ptr[row_idx + 1] - h_csr_row_ptr[row_idx];
            num_chunks += (nnz_in_row + CHUNK_SIZE - 1) / CHUNK_SIZE;
        }

        int *d_chunk_counts_per_row, *d_chunk_write_offsets, *d_chunk_rows_high, *d_chunk_offsets_high;
        CUDA_CHECK(cudaMalloc(&d_chunk_counts_per_row, h_counts[2] * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_write_offsets, h_counts[2] * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_rows_high, num_chunks * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_offsets_high, num_chunks * sizeof(int)));

        CUDA_CHECK(cudaEventRecord(start_high, streams[2]));
        dim3 grid_prep((h_counts[2] + 255) / 256);
        dim3 block_prep(256);
        get_chunk_counts_per_row_kernel<<<grid_prep, block_prep, 0, streams[2]>>>(
            sparse.row_ptr, d_high_rows, d_chunk_counts_per_row, h_counts[2]);
        thrust::exclusive_scan(thrust::cuda::par.on(streams[2]), thrust::device_ptr<int>(d_chunk_counts_per_row),
                               thrust::device_ptr<int>(d_chunk_counts_per_row + h_counts[2]),
                               thrust::device_ptr<int>(d_chunk_write_offsets));
        populate_chunks_kernel<<<grid_prep, block_prep, 0, streams[2]>>>(
            d_high_rows, d_chunk_counts_per_row, d_chunk_write_offsets, d_chunk_rows_high, d_chunk_offsets_high,
            h_counts[2]);

        dim3 grid_high(num_chunks);
        dim3 block_high(HIGH_DENSITY_BLOCK_SIZE);
        sddmm_high_density_extreme_kernel<<<grid_high, block_high, required_mem_high, streams[2]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx, d_chunk_rows_high, d_chunk_offsets_high, sparse.values,
            num_chunks, K);
        CUDA_CHECK(cudaEventRecord(stop_high, streams[2]));

        CUDA_CHECK(cudaFreeAsync(d_chunk_rows_high, streams[2]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_offsets_high, streams[2]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_counts_per_row, streams[2]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_write_offsets, streams[2]));
    }

    // 超高密度区处理 - 新增
    if (h_counts[3] > 0) {
        size_t required_mem_ultra = (size_t) K * sizeof(float);
        std::vector<int> h_ultra_high_rows(h_counts[3]);
        CUDA_CHECK(cudaMemcpy(h_ultra_high_rows.data(), d_ultra_high_rows, h_counts[3] * sizeof(int), cudaMemcpyDeviceToHost));
        int num_chunks_ultra = 0;
        for (int row_idx: h_ultra_high_rows) {
            int nnz_in_row = h_csr_row_ptr[row_idx + 1] - h_csr_row_ptr[row_idx];
            num_chunks_ultra += (nnz_in_row + CHUNK_SIZE - 1) / CHUNK_SIZE;
        }

        int *d_chunk_counts_per_row_ultra, *d_chunk_write_offsets_ultra, *d_chunk_rows_ultra, *d_chunk_offsets_ultra;
        CUDA_CHECK(cudaMalloc(&d_chunk_counts_per_row_ultra, h_counts[3] * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_write_offsets_ultra, h_counts[3] * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_rows_ultra, num_chunks_ultra * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_offsets_ultra, num_chunks_ultra * sizeof(int)));

        CUDA_CHECK(cudaEventRecord(start_ultra, streams[3]));
        dim3 grid_prep_ultra((h_counts[3] + 255) / 256);
        dim3 block_prep_ultra(256);
        get_chunk_counts_per_row_kernel<<<grid_prep_ultra, block_prep_ultra, 0, streams[3]>>>(
            sparse.row_ptr, d_ultra_high_rows, d_chunk_counts_per_row_ultra, h_counts[3]);
        thrust::exclusive_scan(thrust::cuda::par.on(streams[3]), thrust::device_ptr<int>(d_chunk_counts_per_row_ultra),
                               thrust::device_ptr<int>(d_chunk_counts_per_row_ultra + h_counts[3]),
                               thrust::device_ptr<int>(d_chunk_write_offsets_ultra));
        populate_chunks_kernel<<<grid_prep_ultra, block_prep_ultra, 0, streams[3]>>>(
            d_ultra_high_rows, d_chunk_counts_per_row_ultra, d_chunk_write_offsets_ultra, d_chunk_rows_ultra, d_chunk_offsets_ultra,
            h_counts[3]);

        dim3 grid_ultra(num_chunks_ultra);
        dim3 block_ultra(ULTRA_HIGH_DENSITY_BLOCK_SIZE);
        sddmm_ultra_high_density_extreme_kernel<<<grid_ultra, block_ultra, required_mem_ultra, streams[3]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx, d_chunk_rows_ultra, d_chunk_offsets_ultra, sparse.values,
            num_chunks_ultra, K);
        CUDA_CHECK(cudaEventRecord(stop_ultra, streams[3]));

        CUDA_CHECK(cudaFreeAsync(d_chunk_rows_ultra, streams[3]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_offsets_ultra, streams[3]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_counts_per_row_ultra, streams[3]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_write_offsets_ultra, streams[3]));
    }

    for (int i = 0; i < 4; ++i) CUDA_CHECK(cudaStreamSynchronize(streams[i]));
    time_low = time_med = time_high = time_ultra_high = 0.0f;
    if (h_counts[0] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_low, start_low, stop_low));
    if (h_counts[1] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_med, start_med, stop_med));
    if (h_counts[2] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_high, start_high, stop_high));
    if (h_counts[3] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_ultra_high, start_ultra, stop_ultra));

    CUDA_CHECK(cudaEventDestroy(start_low));
    CUDA_CHECK(cudaEventDestroy(stop_low));
    CUDA_CHECK(cudaEventDestroy(start_med));
    CUDA_CHECK(cudaEventDestroy(stop_med));
    CUDA_CHECK(cudaEventDestroy(start_high));
    CUDA_CHECK(cudaEventDestroy(stop_high));
    CUDA_CHECK(cudaEventDestroy(start_ultra));
    CUDA_CHECK(cudaEventDestroy(stop_ultra));
    for (int i = 0; i < 4; ++i) CUDA_CHECK(cudaStreamDestroy(streams[i]));
    CUDA_CHECK(cudaFree(d_low_rows));
    CUDA_CHECK(cudaFree(d_medium_rows));
    CUDA_CHECK(cudaFree(d_high_rows));
    CUDA_CHECK(cudaFree(d_ultra_high_rows));
    CUDA_CHECK(cudaFree(d_low_count));
    CUDA_CHECK(cudaFree(d_medium_count));
    CUDA_CHECK(cudaFree(d_high_count));
    CUDA_CHECK(cudaFree(d_ultra_high_count));
}

// =================================================================
// 辅助函数
// =================================================================
void sddmm_cpu_reference(const float *A, const float *B, const int *row_ptr, const int *col_idx, float *values, int M,
                         int N, int K) {
#pragma omp parallel for schedule(dynamic)
    for (int row = 0; row < M; ++row) {
        int start = row_ptr[row];
        int end = row_ptr[row + 1];
        for (int idx = start; idx < end; ++idx) {
            int col = col_idx[idx];
            float sum = 0.0f;
#pragma GCC ivdep
            for (int k = 0; k < K; ++k) {
                sum += A[(size_t) row * K + k] * B[(size_t) col * K + k];
            }
            values[idx] = sum;
        }
    }
}

void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz, std::vector<int> &coo_rows,
                     std::vector<int> &coo_cols) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "错误: 无法打开矩阵文件: " << filename << std::endl;
        exit(1);
    }
    while (file.peek() == '%') file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    file >> M >> N >> nnz;
    coo_rows.resize(nnz);
    coo_cols.resize(nnz);
    for (int i = 0; i < nnz; ++i) {
        int r, c;
        file >> r >> c;
        coo_rows[i] = r - 1;
        coo_cols[i] = c - 1;
        file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    }
    file.close();
}

void coo_to_csr(int M, int nnz, const std::vector<int> &coo_rows_in, const std::vector<int> &coo_cols_in,
                std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
    csr_row_ptr.assign(M + 1, 0);
    std::vector<std::pair<int, int> > coo(nnz);
    for (int i = 0; i < nnz; ++i) coo[i] = {coo_rows_in[i], coo_cols_in[i]};
    std::sort(coo.begin(), coo.end());
    csr_col_idx.resize(nnz);
    for (int i = 0; i < nnz; ++i) {
        csr_col_idx[i] = coo[i].second;
        csr_row_ptr[coo[i].first + 1]++;
    }
    for (int i = 0; i < M; ++i) csr_row_ptr[i + 1] += csr_row_ptr[i];
}

__global__ void warmup_kernel() {
}

int main(int argc, char **argv) {
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
        return 1;
    }

    std::srand(std::time(nullptr));
    std::string filename = argv[1];
    int K = (argc > 2) ? std::atoi(argv[2]) : 128;

    int device;
    cudaGetDevice(&device);
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device);
    size_t shared_mem_per_block = prop.sharedMemPerBlock;
    printf("=== GPU信息 ===\n设备名称: %s (Compute Capability %d.%d)\n", prop.name, prop.major, prop.minor);
    printf("设备共享内存/块: %zu bytes\n", shared_mem_per_block);
    printf("多处理器数量: %d\n", prop.multiProcessorCount);
    printf("每个多处理器的最大线程数: %d\n\n", prop.maxThreadsPerMultiProcessor);

    int M, N, nnz;
    std::vector<int> coo_rows, coo_cols;
    load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);

    std::vector<int> h_csr_row_ptr, h_csr_col_idx;
    coo_to_csr(M, nnz, coo_rows, coo_cols, h_csr_row_ptr, h_csr_col_idx);

    std::vector<float> h_A((size_t) M * K), h_B((size_t) N * K);
    for (size_t i = 0; i < (size_t) M * K; ++i) h_A[i] = (rand() % 100 + 1) / 100.0f;
    for (size_t i = 0; i < (size_t) N * K; ++i) h_B[i] = (rand() % 100 + 1) / 100.0f;

    float *d_A, *d_B;
    CSRMatrix sparse;
    sparse.rows = M;
    sparse.cols = N;
    sparse.nnz = nnz;
    CUDA_CHECK(cudaMalloc(&d_A, (size_t)M * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_B, (size_t)N * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&sparse.col_idx, (size_t)nnz * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&sparse.values, (size_t)nnz * sizeof(float)));
    CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), (size_t)M * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_B, h_B.data(), (size_t)N * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), (size_t)nnz * sizeof(int), cudaMemcpyHostToDevice));

    printf("=== 矩阵信息 ===\n");
    printf("矩阵文件: %s\n", filename.c_str());
    printf("矩阵维度: M=%d, N=%d, K=%d\n", M, N, K);
    printf("非零元素: %d (稀疏度: %.4f%%)\n\n", nnz, 100.0 * nnz / ((double) M * N));

    std::cout << "预热GPU..." << std::endl;
    warmup_kernel<<<1, 1>>>();
    CUDA_CHECK(cudaDeviceSynchronize());

    printf("=== 开始执行SDDMM极限优化版 ===\n");

    const int num_runs = 5;
    std::vector<float> total_times;
    cudaEvent_t start_total, stop_total;
    CUDA_CHECK(cudaEventCreate(&start_total));
    CUDA_CHECK(cudaEventCreate(&stop_total));

    for (int run = 0; run < num_runs; run++) {
        CUDA_CHECK(cudaMemset(sparse.values, 0, (size_t)nnz * sizeof(float)));
        CUDA_CHECK(cudaDeviceSynchronize());
        float ms_low = 0, ms_med = 0, ms_high = 0, ms_ultra_high = 0;

        CUDA_CHECK(cudaEventRecord(start_total));
        sddmm_extreme_execution(d_A, d_B, sparse, h_csr_row_ptr, K, shared_mem_per_block,
                               ms_low, ms_med, ms_high, ms_ultra_high);
        CUDA_CHECK(cudaEventRecord(stop_total));
        CUDA_CHECK(cudaEventSynchronize(stop_total));

        float ms_total = 0;
        CUDA_CHECK(cudaEventElapsedTime(&ms_total, start_total, stop_total));
        total_times.push_back(ms_total);
        printf("运行 %d: %.3f ms (低: %.3f ms, 中: %.3f ms, 高: %.3f ms, 超高: %.3f ms)\n",
               run + 1, ms_total, ms_low, ms_med, ms_high, ms_ultra_high);
    }
    CUDA_CHECK(cudaEventDestroy(start_total));
    CUDA_CHECK(cudaEventDestroy(stop_total));

    float min_time = *std::min_element(total_times.begin(), total_times.end());
    float avg_time = std::accumulate(total_times.begin(), total_times.end(), 0.0f) / num_runs;
    double gflops = (2.0 * (double) nnz * K) / (min_time * 1e6);
    printf("\n=== 性能统计 ===\n");
    printf("平均总时间: %.3f ms\n", avg_time);
    printf("最佳总时间: %.3f ms\n", min_time);
    printf("峰值性能: %.2f GFLOPS\n", gflops);

    std::cout << "\n验证计算正确性..." << std::endl;
    std::vector<float> h_values_gpu(nnz);
    CUDA_CHECK(cudaMemcpy(h_values_gpu.data(), sparse.values, (size_t)nnz * sizeof(float), cudaMemcpyDeviceToHost));
    std::vector<float> h_values_cpu(nnz, 0.0f);
    auto cpu_start = std::chrono::high_resolution_clock::now();
    sddmm_cpu_reference(h_A.data(), h_B.data(), h_csr_row_ptr.data(), h_csr_col_idx.data(), h_values_cpu.data(), M,
                        N, K);
    auto cpu_end = std::chrono::high_resolution_clock::now();
    auto cpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(cpu_end - cpu_start);

    std::cout << "CPU (OMP) 参考实现时间: " << cpu_duration.count() << " ms" << std::endl;
    if (min_time > 0)
        std::cout << "GPU加速比 (vs CPU OMP): " << std::fixed << std::setprecision(2) << (float) cpu_duration.count() /
                min_time << "x" << std::endl;

    int correct_count = 0;
    float max_error = 0.0f;
    double total_abs_error = 0.0, l1_norm_cpu = 0.0;
    for (int i = 0; i < nnz; i++) {
        float abs_diff = std::abs(h_values_cpu[i] - h_values_gpu[i]);
        total_abs_error += abs_diff;
        l1_norm_cpu += std::abs(h_values_cpu[i]);
        if (abs_diff > max_error) max_error = abs_diff;
        bool is_correct = false;
        if (std::abs(h_values_cpu[i]) > 1e-9) {
            if ((abs_diff / std::abs(h_values_cpu[i])) < 1e-4) is_correct = true;
        } else {
            if (abs_diff < 1e-6) is_correct = true;
        }
        if (is_correct) correct_count++;
    }

    std::cout << "\n=== 验证结果 ===" << std::endl;
    std::cout << std::scientific << "最大绝对误差: " << max_error << std::endl;
    if (l1_norm_cpu > 0) {
        std::cout << "相对L1误差: " << (total_abs_error / l1_norm_cpu) << std::endl;
    }
    std::cout << std::fixed << std::setprecision(4) << "近似正确率: " << (100.0f * correct_count / nnz) << "%" <<
            std::endl;

    CUDA_CHECK(cudaFree(d_A));
    CUDA_CHECK(cudaFree(d_B));
    CUDA_CHECK(cudaFree(sparse.row_ptr));
    CUDA_CHECK(cudaFree(sparse.col_idx));
    CUDA_CHECK(cudaFree(sparse.values));

    std::cout << "\n程序正常结束。" << std::endl;
    return 0;
}
