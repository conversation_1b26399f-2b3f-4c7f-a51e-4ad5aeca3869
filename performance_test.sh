#!/bin/bash

# SDDMM 性能测试脚本

echo "=========================================="
echo "SDDMM 优化版本性能测试"
echo "=========================================="

# 检查程序是否存在
if [ ! -f "sddmm_optimized" ]; then
    echo "错误: 未找到 sddmm_optimized 程序"
    echo "请先运行 ./compile_optimized.sh 编译程序"
    exit 1
fi

# 检查测试矩阵生成器
if [ ! -f "generate_test_matrix" ]; then
    echo "编译测试矩阵生成器..."
    g++ -O3 generate_test_matrix.cpp -o generate_test_matrix
    if [ $? -ne 0 ]; then
        echo "错误: 无法编译测试矩阵生成器"
        exit 1
    fi
fi

# 创建测试目录
mkdir -p test_results

# 测试配置
declare -a MATRIX_SIZES=("500" "1000" "2000" "5000")
declare -a SPARSITIES=("0.01" "0.05" "0.1")
declare -a K_VALUES=("64" "128" "256")

echo ""
echo "=== 开始性能测试 ==="
echo "测试矩阵规模: ${MATRIX_SIZES[*]}"
echo "稀疏度: ${SPARSITIES[*]}"
echo "K值: ${K_VALUES[*]}"
echo ""

# 创建结果文件
RESULT_FILE="test_results/performance_results_$(date +%Y%m%d_%H%M%S).txt"
echo "SDDMM 优化版本性能测试结果" > $RESULT_FILE
echo "测试时间: $(date)" >> $RESULT_FILE
echo "========================================" >> $RESULT_FILE

# 获取GPU信息
echo "GPU信息:" >> $RESULT_FILE
nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader >> $RESULT_FILE
echo "" >> $RESULT_FILE

# 性能测试循环
test_count=0
total_tests=$((${#MATRIX_SIZES[@]} * ${#SPARSITIES[@]} * ${#K_VALUES[@]}))

for size in "${MATRIX_SIZES[@]}"; do
    for sparsity in "${SPARSITIES[@]}"; do
        for k in "${K_VALUES[@]}"; do
            test_count=$((test_count + 1))
            
            echo "[$test_count/$total_tests] 测试: ${size}x${size}, 稀疏度=${sparsity}, K=${k}"
            
            # 生成测试矩阵
            matrix_file="test_results/matrix_${size}_${sparsity}.mtx"
            if [ ! -f "$matrix_file" ]; then
                echo "  生成测试矩阵: $matrix_file"
                ./generate_test_matrix "$matrix_file" "$size" "$size" "$sparsity"
            fi
            
            # 运行性能测试
            echo "  运行SDDMM测试..."
            echo "测试 $test_count: 矩阵=${size}x${size}, 稀疏度=${sparsity}, K=${k}" >> $RESULT_FILE
            echo "----------------------------------------" >> $RESULT_FILE
            
            # 捕获程序输出
            ./sddmm_optimized "$matrix_file" "$k" >> $RESULT_FILE 2>&1
            
            echo "" >> $RESULT_FILE
            echo "========================================" >> $RESULT_FILE
            echo ""
        done
    done
done

echo ""
echo "=== 性能测试完成 ==="
echo "结果保存在: $RESULT_FILE"

# 生成性能摘要
echo ""
echo "=== 生成性能摘要 ==="

SUMMARY_FILE="test_results/performance_summary_$(date +%Y%m%d_%H%M%S).txt"
echo "SDDMM 优化版本性能摘要" > $SUMMARY_FILE
echo "生成时间: $(date)" >> $SUMMARY_FILE
echo "========================================" >> $SUMMARY_FILE

# 提取关键性能数据
echo "矩阵规模,稀疏度,K值,最佳时间(ms),峰值性能(GFLOPS),加速比,选择策略" >> $SUMMARY_FILE

grep -A 20 "测试.*矩阵=" $RESULT_FILE | \
grep -E "(测试|最佳执行时间|峰值性能|GPU加速比|选择策略)" | \
while read line; do
    if [[ $line == *"测试"* ]]; then
        # 提取测试参数
        matrix_info=$(echo $line | sed 's/.*矩阵=\([^,]*\).*/\1/')
        sparsity_info=$(echo $line | sed 's/.*稀疏度=\([^,]*\).*/\1/')
        k_info=$(echo $line | sed 's/.*K=\([0-9]*\).*/\1/')
        current_test="$matrix_info,$sparsity_info,$k_info"
    elif [[ $line == *"最佳执行时间"* ]]; then
        best_time=$(echo $line | sed 's/.*: \([0-9.]*\) ms.*/\1/')
    elif [[ $line == *"峰值性能"* ]]; then
        peak_gflops=$(echo $line | sed 's/.*: \([0-9.]*\) GFLOPS.*/\1/')
    elif [[ $line == *"GPU加速比"* ]]; then
        speedup=$(echo $line | sed 's/.*: \([0-9.]*\)x.*/\1/')
    elif [[ $line == *"选择策略"* ]]; then
        strategy=$(echo $line | sed 's/选择策略: \(.*\) (.*/\1/')
        echo "$current_test,$best_time,$peak_gflops,$speedup,$strategy" >> $SUMMARY_FILE
    fi
done

echo "性能摘要保存在: $SUMMARY_FILE"

# 清理临时文件
echo ""
echo "=== 清理临时文件 ==="
echo "保留测试矩阵文件用于后续测试"
echo "如需清理，请手动删除 test_results/ 目录"

echo ""
echo "测试完成！"
echo "详细结果: $RESULT_FILE"
echo "性能摘要: $SUMMARY_FILE"
