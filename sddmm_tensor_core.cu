#include <cuda_runtime.h>
#include <mma.h>
#include <cuda_fp16.h>

using namespace nvcuda;

// Tensor Core SDDMM内核 (使用半精度)
__global__ void sddmm_tensor_core_kernel(
    const half* __restrict__ A, 
    const half* __restrict__ B,
    const int* __restrict__ row_ptr, 
    const int* __restrict__ col_idx,
    float* __restrict__ result,
    int M, int K) {
    
    // Tensor Core fragment声明
    wmma::fragment<wmma::matrix_a, 16, 16, 16, half, wmma::row_major> a_frag;
    wmma::fragment<wmma::matrix_b, 16, 16, 16, half, wmma::col_major> b_frag;
    wmma::fragment<wmma::accumulator, 16, 16, 16, float> c_frag;
    
    extern __shared__ half shared_mem[];
    half* a_shared = shared_mem;
    half* b_shared = shared_mem + 16 * K;
    
    const int warp_id = (blockIdx.x * blockDim.x + threadIdx.x) / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_grid = (gridDim.x * blockDim.x) / 32;
    
    // 每个warp处理多行
    for (int row_base = warp_id * 16; row_base < M; row_base += warps_per_grid * 16) {
        int rows_to_process = min(16, M - row_base);
        
        // 加载A矩阵的16行到共享内存
        for (int i = 0; i < rows_to_process; ++i) {
            int row = row_base + i;
            for (int k = lane_id; k < K; k += 32) {
                if (k < K) {
                    a_shared[i * K + k] = A[row * K + k];
                }
            }
        }
        __syncwarp();
        
        // 处理每行的非零元素
        for (int i = 0; i < rows_to_process; ++i) {
            int row = row_base + i;
            int row_start = row_ptr[row];
            int row_end = row_ptr[row + 1];
            
            // 按16列为一组处理非零元素
            for (int nnz_base = row_start; nnz_base < row_end; nnz_base += 16) {
                int nnz_count = min(16, row_end - nnz_base);
                
                // 收集列索引并加载对应的B矩阵列
                __shared__ int cols[16];
                if (lane_id < nnz_count) {
                    cols[lane_id] = col_idx[nnz_base + lane_id];
                }
                __syncwarp();
                
                // 加载B矩阵列到共享内存
                for (int j = 0; j < nnz_count; ++j) {
                    int col = cols[j];
                    for (int k = lane_id; k < K; k += 32) {
                        if (k < K) {
                            b_shared[j * K + k] = B[col * K + k];
                        }
                    }
                }
                __syncwarp();
                
                // 使用Tensor Core计算
                if (K >= 16 && nnz_count >= 16) {
                    for (int k_base = 0; k_base < K; k_base += 16) {
                        if (k_base + 16 <= K) {
                            // 加载fragments
                            wmma::load_matrix_sync(a_frag, &a_shared[i * K + k_base], K);
                            wmma::load_matrix_sync(b_frag, &b_shared[k_base], K);
                            wmma::fill_fragment(c_frag, 0.0f);
                            
                            // 执行矩阵乘法
                            wmma::mma_sync(c_frag, a_frag, b_frag, c_frag);
                            
                            // 存储结果
                            float c_result[16 * 16];
                            wmma::store_matrix_sync(c_result, c_frag, 16, wmma::mem_row_major);
                            
                            // 累加到最终结果
                            for (int j = 0; j < min(16, nnz_count); ++j) {
                                if (lane_id == 0) {
                                    float sum = 0.0f;
                                    for (int kk = 0; kk < 16; ++kk) {
                                        sum += c_result[0 * 16 + kk];
                                    }
                                    atomicAdd(&result[nnz_base + j], sum);
                                }
                            }
                        }
                    }
                } else {
                    // 回退到标准计算
                    for (int j = 0; j < nnz_count; ++j) {
                        float sum = 0.0f;
                        for (int k = lane_id; k < K; k += 32) {
                            sum += __half2float(a_shared[i * K + k]) * __half2float(b_shared[j * K + k]);
                        }
                        
                        // Warp reduce
                        for (int offset = 16; offset > 0; offset /= 2) {
                            sum += __shfl_down_sync(0xFFFFFFFF, sum, offset);
                        }
                        
                        if (lane_id == 0) {
                            result[nnz_base + j] = sum;
                        }
                    }
                }
            }
        }
    }
}

// 混合精度转换函数
void convert_to_half(const float* input, half* output, size_t size) {
    dim3 block(256);
    dim3 grid((size + block.x - 1) / block.x);
    
    auto convert_kernel = [] __device__ (const float* in, half* out, size_t n) {
        int idx = blockIdx.x * blockDim.x + threadIdx.x;
        if (idx < n) {
            out[idx] = __float2half(in[idx]);
        }
    };
    
    convert_kernel<<<grid, block>>>(input, output, size);
}

// Tensor Core SDDMM主函数
void sddmm_tensor_core(
    const float* d_A_float, const float* d_B_float,
    const int* d_row_ptr, const int* d_col_idx,
    float* d_result, int M, int N, int K) {
    
    // 转换为半精度
    half *d_A_half, *d_B_half;
    cudaMalloc(&d_A_half, M * K * sizeof(half));
    cudaMalloc(&d_B_half, N * K * sizeof(half));
    
    convert_to_half(d_A_float, d_A_half, M * K);
    convert_to_half(d_B_float, d_B_half, N * K);
    
    // 启动Tensor Core内核
    int num_warps = (M + 15) / 16;
    int blocks = (num_warps + 7) / 8;  // 每个block 8个warp
    int threads_per_block = 8 * 32;
    
    size_t shared_mem_size = (16 * K + 16 * K) * sizeof(half) + 16 * sizeof(int);
    
    sddmm_tensor_core_kernel<<<blocks, threads_per_block, shared_mem_size>>>(
        d_A_half, d_B_half, d_row_ptr, d_col_idx, d_result, M, K);
    
    cudaFree(d_A_half);
    cudaFree(d_B_half);
}
