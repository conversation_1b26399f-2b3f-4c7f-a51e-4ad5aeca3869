#!/bin/bash

echo "========================================"
echo "   SDDMM 革命性优化修复版编译脚本"
echo "========================================"

# 检查CUDA环境
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到nvcc编译器，请确保CUDA已正确安装"
    exit 1
fi

echo "正在编译SDDMM革命性优化修复版..."

# 编译修复版本
echo "[1/1] 编译革命性优化修复版..."
nvcc -o sddmm_revolutionary_fixed sddmm_revolutionary_fixed.cu -O3 -arch=sm_70 -std=c++11 -Xcompiler "-fopenmp"
if [ $? -ne 0 ]; then
    echo "编译失败，尝试不使用OpenMP..."
    nvcc -o sddmm_revolutionary_fixed sddmm_revolutionary_fixed.cu -O3 -arch=sm_70 -std=c++11
    if [ $? -ne 0 ]; then
        echo "编译失败，尝试使用sm_60架构..."
        nvcc -o sddmm_revolutionary_fixed sddmm_revolutionary_fixed.cu -O3 -arch=sm_60 -std=c++11
        if [ $? -ne 0 ]; then
            echo "编译失败"
            exit 1
        fi
    fi
fi

echo ""
echo "✅ 编译成功！"
echo ""
echo "可执行文件: sddmm_revolutionary_fixed"
echo ""
echo "🚀 革命性优化特性 (兼容版):"
echo "  1. 内存层次结构重新设计 - 三级缓存协同优化 (兼容版)"
echo "  2. 动态负载均衡优化 - 避免warp分化"
echo "  3. 自适应算法选择框架 - 智能策略匹配"
echo ""
echo "🔧 修复的兼容性问题:"
echo "  - 移除了cooperative_groups::reduce依赖"
echo "  - 使用手动warp shuffle归约"
echo "  - 修复了thrust::sequence调用"
echo "  - 简化了流水线架构实现"
echo ""

# 询问是否运行测试
read -p "是否运行测试程序? (y/n): " choice
if [[ $choice == "y" || $choice == "Y" ]]; then
    echo ""
    echo "🚀 运行革命性优化测试..."
    echo "========================================"
    
    # 检查是否有测试矩阵文件
    if [ -f "test_matrix.mtx" ]; then
        echo "使用 test_matrix.mtx 进行测试..."
        ./sddmm_revolutionary_fixed test_matrix.mtx 128
    else
        echo "未找到 test_matrix.mtx，请提供矩阵文件"
        echo "用法: ./sddmm_revolutionary_fixed <matrix_file.mtx> [K=128]"
    fi
    
    echo "========================================"
    echo "测试完成！"
else
    echo ""
    echo "💡 使用方法:"
    echo "  ./sddmm_revolutionary_fixed <matrix_file.mtx> [K]"
    echo ""
    echo "示例:"
    echo "  ./sddmm_revolutionary_fixed test_matrix.mtx 128"
    echo "  ./sddmm_revolutionary_fixed large_matrix.mtx 256"
fi

echo ""
echo "📚 优化策略详解 (兼容版):"
echo ""
echo "🧠 内存层次结构重新设计 (兼容版):"
echo "  - 三级缓存策略: 寄存器 → 共享内存 → L2缓存"
echo "  - 缓存感知的数据预取和重排"
echo "  - Bank冲突避免技术"
echo "  - 适用: K>64, 不规则稀疏模式"
echo ""
echo "⚖️ 动态负载均衡优化:"
echo "  - 原子操作工作分配"
echo "  - 避免warp分化"
echo "  - 向量化内存访问"
echo "  - 适用: 负载不均衡的稀疏矩阵"
echo ""
echo "🎯 自适应算法选择框架:"
echo "  - 实时分析矩阵特征"
echo "  - 智能策略选择"
echo "  - 历史性能学习"
echo "  - 适用: 所有场景的智能优化"
echo ""
echo "🔥 预期性能提升: 2-5倍 (兼容版)"
echo ""

echo "程序编译完成！"
