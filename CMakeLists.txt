cmake_minimum_required(VERSION 3.28)
project(CUDAProjects LANGUAGES CUDA CXX)

# ==== 彻底排除Anaconda干扰 ====
# 1. 清除环境变量
unset(ENV{CPATH})
unset(ENV{LIBRARY_PATH})
unset(ENV{LD_LIBRARY_PATH})

# 2. 重置隐式搜索路径
set(CMAKE_CXX_IMPLICIT_INCLUDE_DIRECTORIES "")
set(CMAKE_CXX_IMPLICIT_LINK_DIRECTORIES "")
set(CMAKE_CUDA_IMPLICIT_INCLUDE_DIRECTORIES "")
set(CMAKE_CUDA_IMPLICIT_LINK_DIRECTORIES "")

# 3. 显式设置系统包含路径
set(SYSTEM_INCLUDE_PATHS
        /usr/include
        /usr/local/include
        ${CUDAToolkit_INCLUDE_DIRS}
)

# 4. 显式设置系统库路径
set(SYSTEM_LIBRARY_PATHS
        /usr/lib
        /usr/lib64
        /usr/local/lib
        /usr/local/lib64
)

# 5. 强制使用系统路径
include_directories(BEFORE ${SYSTEM_INCLUDE_PATHS})
link_directories(${SYSTEM_LIBRARY_PATHS})
# =============================

# 添加Abseil库查找
#file(GLOB ABSL_LIBS "/usr/local/lib64/libabsl*.a")
#if(ABSL_LIBS)
#    message(STATUS "Found Abseil libraries: ${ABSL_LIBS}")
#else()
#    message(FATAL_ERROR "Abseil libraries not found in /usr/local/lib")
#endif()
find_package(absl REQUIRED COMPONENTS
        base
        synchronization
        random_random
        random_distributions
        random_internal_platform
        random_internal_randen
        random_internal_randen_hwaccel
        random_internal_randen_slow
)
# =============================

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CUDA_STANDARD 17)

# CUDA编译器标志
set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} --expt-extended-lambda --expt-relaxed-constexpr")

# 显式设置CUDA路径
set(CUDAToolkit_ROOT "/usr/local/cuda")
enable_language(CUDA)
find_package(CUDAToolkit REQUIRED)

set(SPUTNIK_INSTALL_DIR /usr/local/)
include_directories(
        ${SPUTNIK_INSTALL_DIR}/include
        ${CUDAToolkit_INCLUDE_DIRS}
)

set(SOURCE_FILES matrix_utils.cu RoDeSddmm.cu)
add_executable(CUDAProjects
        sddmm_rode_test.cu ${SOURCE_FILES}
        sddmm_18.cu
        sddmm_19.cu
        sddmm_20.cu
        sddmm_21.cu
        sddmm_22.cu
        sddmm_23.cu
        sddmm_24.cu
        sddmm_25.cu
        sddmm_26.cu
        sddmm_27.cu
        sddmm_28.cu
        sddmm_29.cu
        sddmm_30.cu
        sddmm_31.cu
        sddmm_32.cu
        sddmm_33.cu
        sddmm_34.cu
        sddmm_35.cu
        sddmm_36.cu
        sddmm_37.cu
        sddmm_38.cu
        sddmm_39.cu
        sddmm_40.cu
        sddmm_41.cu
        sddmm_42.cu
        sddmm_43.cu
        sddmm_44.cu
        sddmm_45.cu
        sddmm_46.cu
        sddmm_sputnik_test.cu)
set_target_properties(CUDAProjects PROPERTIES
        CUDA_SEPARABLE_COMPILATION ON
        SKIP_BUILD_RPATH TRUE
)

# ==== glog配置 ====
# 手动查找系统glog
find_library(GLOG_LIBRARY
        NAMES glog
        PATHS ${SYSTEM_LIBRARY_PATHS}
        NO_DEFAULT_PATH
        REQUIRED
)

find_path(GLOG_INCLUDE_DIR
        NAMES glog/logging.h
        PATHS ${SYSTEM_INCLUDE_PATHS}
        NO_DEFAULT_PATH
        REQUIRED
)

target_include_directories(CUDAProjects PRIVATE ${GLOG_INCLUDE_DIR})
target_link_libraries(CUDAProjects PRIVATE ${GLOG_LIBRARY})
# =================

# 正确配置Abseil头文件模式（移除编译定义）
target_include_directories(CUDAProjects PRIVATE /usr/local/include)

# 添加必要的C++17特性支持
target_compile_features(CUDAProjects PRIVATE cxx_std_17)
if(CMAKE_CUDA_COMPILER)
    target_compile_options(CUDAProjects PRIVATE
            $<$<COMPILE_LANGUAGE:CUDA>:-Xcompiler=-std=c++17>
    )
endif()

target_link_libraries(CUDAProjects PRIVATE
        ${SPUTNIK_INSTALL_DIR}/lib/libsputnik.so
        CUDA::cublas
        CUDA::cudart
        Threads::Threads
        absl::base
        absl::synchronization
        absl::random_random
        absl::random_distributions
        absl::random_internal_platform
        absl::random_internal_randen
        absl::random_internal_randen_hwaes
        absl::random_internal_randen_slow
)
# 添加RPATH设置
set_target_properties(CUDAProjects PROPERTIES
        BUILD_WITH_INSTALL_RPATH TRUE
        INSTALL_RPATH "\$ORIGIN:/usr/local/lib"
        BUILD_RPATH "\$ORIGIN:/usr/local/lib"
)

target_compile_options(CUDAProjects PRIVATE
        $<$<COMPILE_LANGUAGE:CUDA>:-Xcompiler=-D_FORCE_INLINES>
)

# 添加对CUDA架构的检测
if(CMAKE_CUDA_COMPILER)
    # 自动检测GPU架构
    if(NOT DEFINED CMAKE_CUDA_ARCHITECTURES)
        set(CMAKE_CUDA_ARCHITECTURES "native")
    endif()

    # 打印检测到的架构
    message(STATUS "CUDA Architectures: ${CMAKE_CUDA_ARCHITECTURES}")

    # 添加架构特定的编译选项
    target_compile_options(CUDAProjects PRIVATE
            $<$<COMPILE_LANGUAGE:CUDA>:
            --generate-code=arch=compute_${CMAKE_CUDA_ARCHITECTURES},code=sm_${CMAKE_CUDA_ARCHITECTURES}
            >
    )
endif()

# 添加对Abseil头文件模式的额外支持
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    # 对于GCC编译器，添加必要的C++17特性支持
    target_compile_options(CUDAProjects PRIVATE
            $<$<COMPILE_LANGUAGE:CXX>:-std=c++17>
            $<$<COMPILE_LANGUAGE:CXX>:-D_GLIBCXX_USE_CXX11_ABI=1>
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    # 对于Clang编译器
    target_compile_options(CUDAProjects PRIVATE
            $<$<COMPILE_LANGUAGE:CXX>:-std=c++17>
    )
endif()