#!/usr/bin/env python3
"""
生成测试用的稀疏矩阵文件 (Matrix Market格式)
用于测试SDDMM程序的性能
"""

import random
import sys

def generate_sparse_matrix(rows, cols, density=0.1, filename="large_test_matrix.mtx"):
    """
    生成稀疏矩阵并保存为Matrix Market格式
    
    Args:
        rows: 矩阵行数
        cols: 矩阵列数  
        density: 稀疏度 (非零元素比例)
        filename: 输出文件名
    """
    
    # 计算非零元素数量
    nnz = int(rows * cols * density)
    
    print(f"生成 {rows}x{cols} 稀疏矩阵，密度={density:.1%}，非零元素={nnz}")
    
    # 生成随机的非零元素位置
    positions = set()
    while len(positions) < nnz:
        row = random.randint(1, rows)  # Matrix Market使用1-based索引
        col = random.randint(1, cols)
        positions.add((row, col))
    
    # 转换为列表并排序
    positions = sorted(list(positions))
    
    # 写入文件
    with open(filename, 'w') as f:
        # 写入头部
        f.write("%%MatrixMarket matrix coordinate real general\n")
        f.write("%\n")
        f.write(f"% Generated sparse matrix for SDDMM testing\n")
        f.write(f"% Dimensions: {rows}x{cols}, Density: {density:.1%}\n")
        f.write("%\n")
        f.write(f"{rows} {cols} {len(positions)}\n")
        
        # 写入非零元素
        for i, (row, col) in enumerate(positions):
            value = random.uniform(-1.0, 1.0)  # 随机值
            f.write(f"{row} {col} {value:.6f}\n")
    
    print(f"矩阵已保存到: {filename}")
    
    # 统计每行的非零元素数量分布
    row_nnz = {}
    for row, col in positions:
        row_nnz[row] = row_nnz.get(row, 0) + 1
    
    nnz_counts = list(row_nnz.values())
    if nnz_counts:
        print(f"每行非零元素统计:")
        print(f"  最小: {min(nnz_counts)}")
        print(f"  最大: {max(nnz_counts)}")
        print(f"  平均: {sum(nnz_counts)/len(nnz_counts):.1f}")
        
        # 密度分类统计
        low_density = sum(1 for x in nnz_counts if x <= 32)
        medium_density = sum(1 for x in nnz_counts if 32 < x <= 256)
        high_density = sum(1 for x in nnz_counts if x > 256)
        
        print(f"密度分类:")
        print(f"  低密度行 (≤32): {low_density}")
        print(f"  中密度行 (32-256): {medium_density}")
        print(f"  高密度行 (>256): {high_density}")

def main():
    if len(sys.argv) < 3:
        print("用法: python generate_test_matrix.py <rows> <cols> [density] [filename]")
        print("示例: python generate_test_matrix.py 1000 800 0.05 test_1000x800.mtx")
        sys.exit(1)
    
    rows = int(sys.argv[1])
    cols = int(sys.argv[2])
    density = float(sys.argv[3]) if len(sys.argv) > 3 else 0.1
    filename = sys.argv[4] if len(sys.argv) > 4 else f"test_{rows}x{cols}.mtx"
    
    # 设置随机种子以便复现
    random.seed(42)
    
    generate_sparse_matrix(rows, cols, density, filename)

if __name__ == "__main__":
    main()
