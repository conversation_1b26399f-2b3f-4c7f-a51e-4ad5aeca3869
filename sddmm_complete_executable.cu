#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <ctime>
#include <cstdlib>
#include <limits>
#include <iomanip>
#include <cuda_runtime.h>
#include <cassert>

// Thrust 头文件
#include <thrust/device_ptr.h>
#include <thrust/scan.h>
#include <thrust/execution_policy.h>

// =================================================================
// 常量定义
// =================================================================
const int MEDIUM_DENSITY_THRESHOLD = 32;
const int HIGH_DENSITY_THRESHOLD = 256;
const int CHUNK_SIZE = 256;

// 验证过的最佳参数
const int LOW_DENSITY_BLOCK_SIZE = 256;
const int MEDIUM_DENSITY_BLOCK_SIZE = 1024;
const int CHUNK_PER_BLOCK_KERNEL_SIZE = 1024;
const int WARP_SIZE = 32;

#define CUDA_CHECK(err) { \
    cudaError_t e = err; \
    if (e != cudaSuccess) { \
        printf("Cuda error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
        exit(EXIT_FAILURE); \
    } \
}

struct CSRMatrix {
    int *row_ptr, *col_idx;
    float *values;
    int rows, cols, nnz;
};

// =================================================================
// 矩阵读取函数
// =================================================================
bool read_matrix_market(const std::string& filename, std::vector<int>& row_indices, 
                       std::vector<int>& col_indices, std::vector<float>& values,
                       int& rows, int& cols, int& nnz) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filename << std::endl;
        return false;
    }

    std::string line;
    // 跳过注释行
    do {
        std::getline(file, line);
    } while (line[0] == '%');

    // 读取矩阵维度
    std::istringstream iss(line);
    iss >> rows >> cols >> nnz;

    row_indices.resize(nnz);
    col_indices.resize(nnz);
    values.resize(nnz);

    // 读取矩阵数据
    for (int i = 0; i < nnz; i++) {
        std::getline(file, line);
        std::istringstream data_iss(line);
        data_iss >> row_indices[i] >> col_indices[i] >> values[i];
        // 转换为0-based索引
        row_indices[i]--;
        col_indices[i]--;
    }

    file.close();
    return true;
}

// 将COO格式转换为CSR格式
void coo_to_csr(const std::vector<int>& row_indices, const std::vector<int>& col_indices,
                const std::vector<float>& values, int rows, int nnz,
                std::vector<int>& csr_row_ptr, std::vector<int>& csr_col_idx, 
                std::vector<float>& csr_values) {
    
    csr_row_ptr.resize(rows + 1, 0);
    csr_col_idx.resize(nnz);
    csr_values.resize(nnz);

    // 计算每行的非零元素数量
    for (int i = 0; i < nnz; i++) {
        csr_row_ptr[row_indices[i] + 1]++;
    }

    // 计算累积和得到行指针
    for (int i = 1; i <= rows; i++) {
        csr_row_ptr[i] += csr_row_ptr[i - 1];
    }

    // 填充列索引和值
    std::vector<int> temp_row_ptr = csr_row_ptr;
    for (int i = 0; i < nnz; i++) {
        int row = row_indices[i];
        int pos = temp_row_ptr[row]++;
        csr_col_idx[pos] = col_indices[i];
        csr_values[pos] = values[i];
    }
}

// 生成随机稠密矩阵
void generate_dense_matrices(float* A, float* B, int M, int N, int K) {
    srand(42); // 固定种子以便复现
    
    for (int i = 0; i < M * K; i++) {
        A[i] = static_cast<float>(rand()) / RAND_MAX * 2.0f - 1.0f;
    }
    
    for (int i = 0; i < N * K; i++) {
        B[i] = static_cast<float>(rand()) / RAND_MAX * 2.0f - 1.0f;
    }
}

// CPU参考实现
void sddmm_cpu_reference(const float* A, const float* B, const std::vector<int>& row_ptr,
                        const std::vector<int>& col_idx, std::vector<float>& values,
                        int M, int N, int K) {
    for (int row = 0; row < M; row++) {
        int start = row_ptr[row];
        int end = row_ptr[row + 1];
        for (int idx = start; idx < end; idx++) {
            int col = col_idx[idx];
            float sum = 0.0f;
            for (int k = 0; k < K; k++) {
                sum += A[row * K + k] * B[col * K + k];
            }
            values[idx] = sum;
        }
    }
}

// =================================================================
// 预处理与分类核函数
// =================================================================
__global__ void get_chunk_counts_per_row_kernel(
    const int *row_ptr, const int *d_high_rows, int *d_chunk_counts_per_row, int num_high_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_high_rows) return;
    int row = d_high_rows[idx];
    int nnz_in_row = row_ptr[row + 1] - row_ptr[row];
    d_chunk_counts_per_row[idx] = (nnz_in_row + CHUNK_SIZE - 1) / CHUNK_SIZE;
}

__global__ void populate_chunks_kernel(
    const int *d_high_rows, const int *d_chunk_counts_per_row, const int *d_chunk_write_offsets,
    int *d_chunk_rows_high, int *d_chunk_offsets_high, int num_high_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_high_rows) return;
    int row = d_high_rows[idx];
    int num_chunks_for_this_row = d_chunk_counts_per_row[idx];
    int write_pos_start = d_chunk_write_offsets[idx];
    for (int i = 0; i < num_chunks_for_this_row; ++i) {
        d_chunk_rows_high[write_pos_start + i] = row;
        d_chunk_offsets_high[write_pos_start + i] = i * CHUNK_SIZE;
    }
}

__global__ void classify_rows_multi_level_kernel(
    const int *row_ptr, int rows,
    int *d_low_rows, int *d_medium_rows, int *d_high_rows,
    int *low_count, int *medium_count, int *high_count) {
    int row = blockIdx.x * blockDim.x + threadIdx.x;
    if (row >= rows) return;
    int nnz = row_ptr[row + 1] - row_ptr[row];
    if (nnz > 0 && nnz <= MEDIUM_DENSITY_THRESHOLD) {
        int pos = atomicAdd(low_count, 1);
        d_low_rows[pos] = row;
    } else if (nnz > MEDIUM_DENSITY_THRESHOLD && nnz <= HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(medium_count, 1);
        d_medium_rows[pos] = row;
    } else if (nnz > HIGH_DENSITY_THRESHOLD) {
        int pos = atomicAdd(high_count, 1);
        d_high_rows[pos] = row;
    }
}

// =================================================================
// 计算内核
// =================================================================

// 低密度区核函数
__global__ __launch_bounds__(LOW_DENSITY_BLOCK_SIZE, 1)
void sddmm_low_density_final_kernel_fixed(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {
    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;
    const int global_warp_id = blockIdx.x * warps_per_block + warp_id;
    if (global_warp_id >= num_rows) return;
    extern __shared__ float s_A[];
    float *a_row_s = &s_A[warp_id * K];
    const int row = row_indices[global_warp_id];
    for (int k = lane_id; k < K; k += 32) { a_row_s[k] = A[(size_t) row * K + k]; }
    __syncthreads();
    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;
    for (int nnz_idx = 0; nnz_idx < nnz_in_row; ++nnz_idx) {
        const int global_idx = row_start + nnz_idx;
        const int col = col_idx[global_idx];
        float partial_sum = 0.0f;
#pragma unroll
        for (int k = lane_id; k < K; k += 32) { partial_sum += a_row_s[k] * B[(size_t) col * K + k]; }
        for (int offset = 16; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }
        if (lane_id == 0) { result[global_idx] = partial_sum; }
    }
}

// 中密度区核函数
__global__ __launch_bounds__(MEDIUM_DENSITY_BLOCK_SIZE, 1)
void sddmm_row_per_block_2d_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_rows, float *__restrict__ result,
    int num_rows, int K) {
    int row_idx = blockIdx.x;
    if (row_idx >= num_rows) return;

    extern __shared__ float a_row_s[];
    int row = d_rows[row_idx];

    for (int k = threadIdx.x + threadIdx.y * blockDim.x; k < K; k += blockDim.x * blockDim.y) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    const int lane_id = threadIdx.x;
    const int warp_y_id = threadIdx.y;
    const int warps_in_block_y = blockDim.y;

    int row_start = row_ptr[row];
    int nnz_in_row = row_ptr[row + 1] - row_start;

    for (int nnz_offset = warp_y_id; nnz_offset < nnz_in_row; nnz_offset += warps_in_block_y) {
        int global_idx = row_start + nnz_offset;
        int col = col_idx[global_idx];
        float partial_sum = 0.0f;

#pragma unroll
        for (int k = lane_id; k < K; k += WARP_SIZE) {
            partial_sum += a_row_s[k] * B[(size_t) col * K + k];
        }

        for (int offset = 16; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// 高密度区核函数
__global__ __launch_bounds__(CHUNK_PER_BLOCK_KERNEL_SIZE, 1)
void sddmm_chunk_per_block_2d_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_chunk_rows,
    const int *__restrict__ d_chunk_offsets,
    float *__restrict__ result,
    int num_chunks, int K) {
    int chunk_id = blockIdx.x;
    if (chunk_id >= num_chunks) return;

    extern __shared__ float a_row_s[];
    int row = d_chunk_rows[chunk_id];

    for (int k = threadIdx.x + threadIdx.y * blockDim.x; k < K; k += blockDim.x * blockDim.y) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    const int lane_id = threadIdx.x;
    const int warp_y_id = threadIdx.y;
    const int warps_in_block_y = blockDim.y;

    int chunk_nnz_start_offset = d_chunk_offsets[chunk_id];
    int row_start_in_csr = row_ptr[row];
    int nnz_in_row = row_ptr[row + 1] - row_start_in_csr;
    int nnz_in_chunk = min(CHUNK_SIZE, nnz_in_row - chunk_nnz_start_offset);

    for (int nnz_offset_in_chunk = warp_y_id; nnz_offset_in_chunk < nnz_in_chunk;
         nnz_offset_in_chunk += warps_in_block_y) {
        int global_idx = row_start_in_csr + chunk_nnz_start_offset + nnz_offset_in_chunk;
        int col = col_idx[global_idx];
        float partial_sum = 0.0f;

#pragma unroll
        for (int k = lane_id; k < K; k += WARP_SIZE) {
            partial_sum += a_row_s[k] * B[(size_t) col * K + k];
        }

        for (int offset = 16; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        if (lane_id == 0) {
            result[global_idx] = partial_sum;
        }
    }
}

// =================================================================
// 主要SDDMM函数
// =================================================================
void sddmm_csr_gpu(const float *d_A, const float *d_B, CSRMatrix &sparse, int K) {
    const int MAX_STREAMS = 4;
    cudaStream_t streams[MAX_STREAMS];
    for (int i = 0; i < MAX_STREAMS; i++) {
        CUDA_CHECK(cudaStreamCreate(&streams[i]));
    }

    // 获取设备属性
    cudaDeviceProp prop;
    CUDA_CHECK(cudaGetDeviceProperties(&prop, 0));
    size_t shared_mem_per_block = prop.sharedMemPerBlock;

    // 分配内存用于行分类
    int *d_low_rows, *d_medium_rows, *d_high_rows;
    int *d_low_count, *d_medium_count, *d_high_count;

    CUDA_CHECK(cudaMalloc(&d_low_rows, sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_rows, sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_rows, sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_low_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_count, sizeof(int)));

    CUDA_CHECK(cudaMemset(d_low_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_medium_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_high_count, 0, sizeof(int)));

    // 行分类
    dim3 classify_block(256);
    dim3 grid_classify((sparse.rows + classify_block.x - 1) / classify_block.x);
    classify_rows_multi_level_kernel<<<grid_classify, classify_block>>>(
        sparse.row_ptr, sparse.rows, d_low_rows, d_medium_rows, d_high_rows,
        d_low_count, d_medium_count, d_high_count);
    CUDA_CHECK(cudaDeviceSynchronize());

    // 获取分类结果
    int h_counts[3];
    CUDA_CHECK(cudaMemcpy(&h_counts[0], d_low_count, sizeof(int), cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(&h_counts[1], d_medium_count, sizeof(int), cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(&h_counts[2], d_high_count, sizeof(int), cudaMemcpyDeviceToHost));

    printf("行分类结果: 低密度=%d, 中密度=%d, 高密度=%d\n", h_counts[0], h_counts[1], h_counts[2]);

    // 创建事件用于计时
    cudaEvent_t start_low, stop_low, start_med, stop_med, start_high, stop_high;
    CUDA_CHECK(cudaEventCreate(&start_low));
    CUDA_CHECK(cudaEventCreate(&stop_low));
    CUDA_CHECK(cudaEventCreate(&start_med));
    CUDA_CHECK(cudaEventCreate(&stop_med));
    CUDA_CHECK(cudaEventCreate(&start_high));
    CUDA_CHECK(cudaEventCreate(&stop_high));

    float time_low = 0.0f, time_med = 0.0f, time_high = 0.0f;

    // === 低密度区处理 ===
    if (h_counts[0] > 0) {
        size_t required_mem_low = (size_t) K * sizeof(float) * (LOW_DENSITY_BLOCK_SIZE / WARP_SIZE);
        if (required_mem_low > shared_mem_per_block) {
            printf("警告: 低密度区需要共享内存 %zu bytes，超出限制 %zu bytes\n",
                   required_mem_low, shared_mem_per_block);
            time_low = -1.0f;
        } else {
            CUDA_CHECK(cudaEventRecord(start_low, streams[0]));
            const int warps_per_block = LOW_DENSITY_BLOCK_SIZE / WARP_SIZE;
            dim3 block_low(LOW_DENSITY_BLOCK_SIZE);
            dim3 grid_low((h_counts[0] + warps_per_block - 1) / warps_per_block);
            sddmm_low_density_final_kernel_fixed<<<grid_low, block_low, required_mem_low, streams[0]>>>(
                d_A, d_B, sparse.row_ptr, sparse.col_idx, d_low_rows, sparse.values, h_counts[0], K);
            CUDA_CHECK(cudaEventRecord(stop_low, streams[0]));
        }
    }

    // === 中密度区处理 ===
    if (h_counts[1] > 0) {
        size_t required_mem_medium = (size_t) K * sizeof(float);
        if (required_mem_medium > shared_mem_per_block) {
            printf("警告: 中密度区需要共享内存 %zu bytes，超出限制 %zu bytes\n",
                   required_mem_medium, shared_mem_per_block);
            time_med = -1.0f;
        } else {
            CUDA_CHECK(cudaEventRecord(start_med, streams[1]));
            const int Y_DIM_MED = MEDIUM_DENSITY_BLOCK_SIZE / WARP_SIZE;
            dim3 block_med_2d(WARP_SIZE, Y_DIM_MED);
            sddmm_row_per_block_2d_kernel<<<h_counts[1], block_med_2d, required_mem_medium, streams[1]>>>(
                d_A, d_B, sparse.row_ptr, sparse.col_idx, d_medium_rows, sparse.values, h_counts[1], K);
            CUDA_CHECK(cudaEventRecord(stop_med, streams[1]));
        }
    }

    // === 高密度区处理 ===
    if (h_counts[2] > 0) {
        // 计算chunks
        int *d_chunk_counts_per_row, *d_chunk_write_offsets;
        CUDA_CHECK(cudaMalloc(&d_chunk_counts_per_row, h_counts[2] * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_write_offsets, h_counts[2] * sizeof(int)));

        dim3 chunk_count_block(256);
        dim3 chunk_count_grid((h_counts[2] + chunk_count_block.x - 1) / chunk_count_block.x);
        get_chunk_counts_per_row_kernel<<<chunk_count_grid, chunk_count_block>>>(
            sparse.row_ptr, d_high_rows, d_chunk_counts_per_row, h_counts[2]);
        CUDA_CHECK(cudaDeviceSynchronize());

        // 计算前缀和
        thrust::device_ptr<int> d_chunk_counts_ptr(d_chunk_counts_per_row);
        thrust::device_ptr<int> d_chunk_write_offsets_ptr(d_chunk_write_offsets);
        thrust::exclusive_scan(thrust::device, d_chunk_counts_ptr, d_chunk_counts_ptr + h_counts[2], d_chunk_write_offsets_ptr);

        int total_chunks;
        CUDA_CHECK(cudaMemcpy(&total_chunks, d_chunk_write_offsets + h_counts[2] - 1, sizeof(int), cudaMemcpyDeviceToHost));
        int last_chunk_count;
        CUDA_CHECK(cudaMemcpy(&last_chunk_count, d_chunk_counts_per_row + h_counts[2] - 1, sizeof(int), cudaMemcpyDeviceToHost));
        total_chunks += last_chunk_count;

        if (total_chunks > 0) {
            int *d_chunk_rows_high, *d_chunk_offsets_high;
            CUDA_CHECK(cudaMalloc(&d_chunk_rows_high, total_chunks * sizeof(int)));
            CUDA_CHECK(cudaMalloc(&d_chunk_offsets_high, total_chunks * sizeof(int)));

            populate_chunks_kernel<<<chunk_count_grid, chunk_count_block>>>(
                d_high_rows, d_chunk_counts_per_row, d_chunk_write_offsets,
                d_chunk_rows_high, d_chunk_offsets_high, h_counts[2]);
            CUDA_CHECK(cudaDeviceSynchronize());

            size_t required_mem_high = (size_t) K * sizeof(float);
            if (required_mem_high <= shared_mem_per_block) {
                CUDA_CHECK(cudaEventRecord(start_high, streams[2]));
                const int Y_DIM_HIGH = CHUNK_PER_BLOCK_KERNEL_SIZE / WARP_SIZE;
                dim3 block_high_2d(WARP_SIZE, Y_DIM_HIGH);
                sddmm_chunk_per_block_2d_kernel<<<total_chunks, block_high_2d, required_mem_high, streams[2]>>>(
                    d_A, d_B, sparse.row_ptr, sparse.col_idx, d_chunk_rows_high,
                    d_chunk_offsets_high, sparse.values, total_chunks, K);
                CUDA_CHECK(cudaEventRecord(stop_high, streams[2]));
            } else {
                printf("警告: 高密度区需要共享内存 %zu bytes，超出限制 %zu bytes\n",
                       required_mem_high, shared_mem_per_block);
                time_high = -1.0f;
            }

            CUDA_CHECK(cudaFree(d_chunk_rows_high));
            CUDA_CHECK(cudaFree(d_chunk_offsets_high));
        }

        CUDA_CHECK(cudaFree(d_chunk_counts_per_row));
        CUDA_CHECK(cudaFree(d_chunk_write_offsets));
    }

    // 同步所有流并计算时间
    for (int i = 0; i < MAX_STREAMS; i++) {
        CUDA_CHECK(cudaStreamSynchronize(streams[i]));
    }

    if (h_counts[0] > 0 && time_low >= 0) {
        CUDA_CHECK(cudaEventElapsedTime(&time_low, start_low, stop_low));
        printf("低密度区计算时间: %.3f ms\n", time_low);
    }
    if (h_counts[1] > 0 && time_med >= 0) {
        CUDA_CHECK(cudaEventElapsedTime(&time_med, start_med, stop_med));
        printf("中密度区计算时间: %.3f ms\n", time_med);
    }
    if (h_counts[2] > 0 && time_high >= 0) {
        CUDA_CHECK(cudaEventElapsedTime(&time_high, start_high, stop_high));
        printf("高密度区计算时间: %.3f ms\n", time_high);
    }

    // 清理资源
    CUDA_CHECK(cudaFree(d_low_rows));
    CUDA_CHECK(cudaFree(d_medium_rows));
    CUDA_CHECK(cudaFree(d_high_rows));
    CUDA_CHECK(cudaFree(d_low_count));
    CUDA_CHECK(cudaFree(d_medium_count));
    CUDA_CHECK(cudaFree(d_high_count));

    for (int i = 0; i < MAX_STREAMS; i++) {
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
    }

    CUDA_CHECK(cudaEventDestroy(start_low));
    CUDA_CHECK(cudaEventDestroy(stop_low));
    CUDA_CHECK(cudaEventDestroy(start_med));
    CUDA_CHECK(cudaEventDestroy(stop_med));
    CUDA_CHECK(cudaEventDestroy(start_high));
    CUDA_CHECK(cudaEventDestroy(stop_high));
}

// =================================================================
// 验证函数
// =================================================================
bool verify_results(const std::vector<float>& gpu_result, const std::vector<float>& cpu_result,
                   float tolerance = 1e-4f) {
    if (gpu_result.size() != cpu_result.size()) {
        printf("结果大小不匹配: GPU=%zu, CPU=%zu\n", gpu_result.size(), cpu_result.size());
        return false;
    }

    int error_count = 0;
    float max_error = 0.0f;
    const int max_print_errors = 10;

    for (size_t i = 0; i < gpu_result.size(); i++) {
        float error = std::abs(gpu_result[i] - cpu_result[i]);
        max_error = std::max(max_error, error);

        if (error > tolerance) {
            error_count++;
            if (error_count <= max_print_errors) {
                printf("错误[%zu]: GPU=%.6f, CPU=%.6f, 差值=%.6f\n",
                       i, gpu_result[i], cpu_result[i], error);
            }
        }
    }

    printf("验证结果: 错误数量=%d/%zu, 最大误差=%.6e\n",
           error_count, gpu_result.size(), max_error);

    return error_count == 0;
}

// =================================================================
// 主函数
// =================================================================
int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> <K>" << std::endl;
        return 1;
    }

    std::string filename = argv[1];
    int K = std::atoi(argv[2]);

    if (K <= 0) {
        std::cerr << "K必须是正整数" << std::endl;
        return 1;
    }

    printf("=== SDDMM 完整可执行程序 ===\n");
    printf("矩阵文件: %s\n", filename.c_str());
    printf("K维度: %d\n", K);

    // 读取矩阵
    std::vector<int> row_indices, col_indices;
    std::vector<float> values;
    int M, N, nnz;

    if (!read_matrix_market(filename, row_indices, col_indices, values, M, N, nnz)) {
        std::cerr << "读取矩阵文件失败" << std::endl;
        return 1;
    }

    printf("矩阵维度: %d x %d, 非零元素: %d\n", M, N, nnz);

    // 转换为CSR格式
    std::vector<int> csr_row_ptr, csr_col_idx;
    std::vector<float> csr_values;
    coo_to_csr(row_indices, col_indices, values, M, nnz, csr_row_ptr, csr_col_idx, csr_values);

    // 生成稠密矩阵
    std::vector<float> A(M * K), B(N * K);
    generate_dense_matrices(A.data(), B.data(), M, N, K);

    // 分配GPU内存
    float *d_A, *d_B;
    CSRMatrix d_sparse;

    CUDA_CHECK(cudaMalloc(&d_A, M * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_B, N * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_sparse.row_ptr, (M + 1) * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_sparse.col_idx, nnz * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_sparse.values, nnz * sizeof(float)));

    d_sparse.rows = M;
    d_sparse.cols = N;
    d_sparse.nnz = nnz;

    // 复制数据到GPU
    CUDA_CHECK(cudaMemcpy(d_A, A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_B, B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_sparse.row_ptr, csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_sparse.col_idx, csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice));

    // GPU计算
    printf("\n=== GPU计算 ===\n");
    auto gpu_start = std::chrono::high_resolution_clock::now();
    sddmm_csr_gpu(d_A, d_B, d_sparse, K);
    CUDA_CHECK(cudaDeviceSynchronize());
    auto gpu_end = std::chrono::high_resolution_clock::now();

    auto gpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(gpu_end - gpu_start);
    printf("GPU总计算时间: %ld ms\n", gpu_duration.count());

    // 复制结果回CPU
    std::vector<float> gpu_result(nnz);
    CUDA_CHECK(cudaMemcpy(gpu_result.data(), d_sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost));

    // CPU参考计算
    printf("\n=== CPU参考计算 ===\n");
    std::vector<float> cpu_result = csr_values; // 复制原始值
    auto cpu_start = std::chrono::high_resolution_clock::now();
    sddmm_cpu_reference(A.data(), B.data(), csr_row_ptr, csr_col_idx, cpu_result, M, N, K);
    auto cpu_end = std::chrono::high_resolution_clock::now();

    auto cpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(cpu_end - cpu_start);
    printf("CPU计算时间: %ld ms\n", cpu_duration.count());

    // 验证结果
    printf("\n=== 结果验证 ===\n");
    bool is_correct = verify_results(gpu_result, cpu_result);

    if (is_correct) {
        printf("✓ 计算结果正确!\n");
        double speedup = static_cast<double>(cpu_duration.count()) / gpu_duration.count();
        printf("加速比: %.2fx\n", speedup);
    } else {
        printf("✗ 计算结果不正确!\n");
    }

    // 性能统计
    printf("\n=== 性能统计 ===\n");
    double gflops = (2.0 * nnz * K) / (gpu_duration.count() * 1e6);
    printf("GPU性能: %.2f GFLOPS\n", gflops);

    // 清理内存
    CUDA_CHECK(cudaFree(d_A));
    CUDA_CHECK(cudaFree(d_B));
    CUDA_CHECK(cudaFree(d_sparse.row_ptr));
    CUDA_CHECK(cudaFree(d_sparse.col_idx));
    CUDA_CHECK(cudaFree(d_sparse.values));

    printf("\n程序执行完成!\n");
    return 0;
}
