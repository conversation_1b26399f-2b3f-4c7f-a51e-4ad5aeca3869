#include <cuda_runtime.h>
#include <vector>
#include <algorithm>

// 多流并行 + 数据预取策略
class SDDMMMultiStreamPrefetch {
private:
    static const int NUM_STREAMS = 8;
    static const int PREFETCH_DEPTH = 2;
    
    cudaStream_t streams[NUM_STREAMS];
    cudaEvent_t events[NUM_STREAMS];
    
    // 分段数据结构
    struct Segment {
        int start_row;
        int end_row;
        int start_nnz;
        int end_nnz;
        float* a_buffer;
        int* row_ptr_buffer;
        int* col_idx_buffer;
        float* result_buffer;
    };
    
    std::vector<Segment> segments;
    
public:
    SDDMMMultiStreamPrefetch() {
        for (int i = 0; i < NUM_STREAMS; ++i) {
            cudaStreamCreate(&streams[i]);
            cudaEventCreate(&events[i]);
        }
    }
    
    ~SDDMMMultiStreamPrefetch() {
        for (int i = 0; i < NUM_STREAMS; ++i) {
            cudaStreamDestroy(streams[i]);
            cudaEventDestroy(events[i]);
        }
        
        for (auto& seg : segments) {
            cudaFree(seg.a_buffer);
            cudaFree(seg.row_ptr_buffer);
            cudaFree(seg.col_idx_buffer);
            cudaFree(seg.result_buffer);
        }
    }
    
    // 智能分段策略
    void create_segments(const std::vector<int>& row_ptr, int M, int K, int nnz) {
        segments.clear();
        
        // 计算每行的工作量
        std::vector<std::pair<int, int>> row_workload;
        for (int i = 0; i < M; ++i) {
            int work = (row_ptr[i + 1] - row_ptr[i]) * K;
            row_workload.push_back({work, i});
        }
        
        // 按工作量排序
        std::sort(row_workload.begin(), row_workload.end(), std::greater<>());
        
        // 均匀分配到各个段
        int total_work = 0;
        for (auto& p : row_workload) total_work += p.first;
        int work_per_segment = total_work / NUM_STREAMS;
        
        int current_work = 0;
        int segment_id = 0;
        std::vector<std::vector<int>> segment_rows(NUM_STREAMS);
        
        for (auto& p : row_workload) {
            segment_rows[segment_id].push_back(p.second);
            current_work += p.first;
            
            if (current_work >= work_per_segment && segment_id < NUM_STREAMS - 1) {
                segment_id++;
                current_work = 0;
            }
        }
        
        // 创建段
        for (int i = 0; i < NUM_STREAMS; ++i) {
            if (segment_rows[i].empty()) continue;
            
            Segment seg;
            std::sort(segment_rows[i].begin(), segment_rows[i].end());
            
            seg.start_row = segment_rows[i].front();
            seg.end_row = segment_rows[i].back() + 1;
            seg.start_nnz = row_ptr[seg.start_row];
            seg.end_nnz = row_ptr[seg.end_row];
            
            int seg_rows = seg.end_row - seg.start_row;
            int seg_nnz = seg.end_nnz - seg.start_nnz;
            
            // 分配GPU内存
            cudaMalloc(&seg.a_buffer, seg_rows * K * sizeof(float));
            cudaMalloc(&seg.row_ptr_buffer, (seg_rows + 1) * sizeof(int));
            cudaMalloc(&seg.col_idx_buffer, seg_nnz * sizeof(int));
            cudaMalloc(&seg.result_buffer, seg_nnz * sizeof(float));
            
            segments.push_back(seg);
        }
    }
    
    // 异步数据传输
    void async_transfer_data(const float* h_A, const int* h_row_ptr, 
                           const int* h_col_idx, int K) {
        for (size_t i = 0; i < segments.size(); ++i) {
            auto& seg = segments[i];
            int stream_id = i % NUM_STREAMS;
            
            // 异步传输A矩阵段
            cudaMemcpyAsync(seg.a_buffer, 
                          &h_A[seg.start_row * K],
                          (seg.end_row - seg.start_row) * K * sizeof(float),
                          cudaMemcpyHostToDevice, streams[stream_id]);
            
            // 异步传输行指针
            cudaMemcpyAsync(seg.row_ptr_buffer,
                          &h_row_ptr[seg.start_row],
                          (seg.end_row - seg.start_row + 1) * sizeof(int),
                          cudaMemcpyHostToDevice, streams[stream_id]);
            
            // 异步传输列索引
            cudaMemcpyAsync(seg.col_idx_buffer,
                          &h_col_idx[seg.start_nnz],
                          (seg.end_nnz - seg.start_nnz) * sizeof(int),
                          cudaMemcpyHostToDevice, streams[stream_id]);
        }
    }
    
    // 预取感知的SDDMM内核
    __global__ void sddmm_prefetch_kernel(
        const float* __restrict__ A, 
        const float* __restrict__ B,
        const int* __restrict__ row_ptr, 
        const int* __restrict__ col_idx,
        float* __restrict__ result,
        int start_row, int num_rows, int K, int start_nnz) {
        
        extern __shared__ float shared_mem[];
        float* a_cache = shared_mem;
        float* b_cache = shared_mem + blockDim.x * K / 32;
        
        const int tid = threadIdx.x;
        const int bid = blockIdx.x;
        const int warp_id = tid / 32;
        const int lane_id = tid % 32;
        
        // 预取下一个warp的数据
        __shared__ int next_row_prefetch;
        __shared__ int next_cols_prefetch[32];
        
        for (int row_offset = bid; row_offset < num_rows; row_offset += gridDim.x) {
            int row = start_row + row_offset;
            int local_row_start = row_ptr[row] - start_nnz;
            int local_row_end = row_ptr[row + 1] - start_nnz;
            
            // 预取A矩阵行
            for (int k = lane_id; k < K; k += 32) {
                a_cache[warp_id * K + k] = A[row_offset * K + k];
            }
            
            // 预取下一行信息
            if (tid == 0 && row_offset + gridDim.x < num_rows) {
                next_row_prefetch = start_row + row_offset + gridDim.x;
            }
            
            __syncthreads();
            
            // 处理当前行的非零元素
            for (int nnz_idx = local_row_start + warp_id; 
                 nnz_idx < local_row_end; nnz_idx += blockDim.x / 32) {
                
                int col = col_idx[nnz_idx];
                
                // 预取B矩阵列
                if (nnz_idx + blockDim.x / 32 < local_row_end) {
                    int next_col = col_idx[nnz_idx + blockDim.x / 32];
                    if (lane_id == 0) {
                        next_cols_prefetch[warp_id] = next_col;
                    }
                }
                
                float sum = 0.0f;
                
                // 向量化内积计算
                #pragma unroll 8
                for (int k = lane_id; k < K; k += 32) {
                    sum += a_cache[warp_id * K + k] * B[col * K + k];
                }
                
                // Warp reduce
                #pragma unroll
                for (int offset = 16; offset > 0; offset /= 2) {
                    sum += __shfl_down_sync(0xFFFFFFFF, sum, offset);
                }
                
                if (lane_id == 0) {
                    result[nnz_idx] = sum;
                }
            }
            __syncthreads();
        }
    }
    
    // 执行多流SDDMM
    void execute(const float* d_B, int N, int K) {
        for (size_t i = 0; i < segments.size(); ++i) {
            auto& seg = segments[i];
            int stream_id = i % NUM_STREAMS;
            
            int num_rows = seg.end_row - seg.start_row;
            int blocks = min(1024, (num_rows + 7) / 8);
            int threads = 256;
            
            size_t shared_mem = threads * K / 32 * sizeof(float) * 2 + 32 * sizeof(int);
            
            sddmm_prefetch_kernel<<<blocks, threads, shared_mem, streams[stream_id]>>>(
                seg.a_buffer, d_B, seg.row_ptr_buffer, seg.col_idx_buffer,
                seg.result_buffer, seg.start_row, num_rows, K, seg.start_nnz);
            
            cudaEventRecord(events[stream_id], streams[stream_id]);
        }
        
        // 等待所有流完成
        for (int i = 0; i < NUM_STREAMS; ++i) {
            cudaEventSynchronize(events[i]);
        }
    }
};
