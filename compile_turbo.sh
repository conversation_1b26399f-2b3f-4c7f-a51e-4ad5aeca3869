#!/bin/bash

echo "=========================================="
echo "SDDMM 涡轮增压版本编译脚本"
echo "=========================================="

# 检查CUDA是否安装
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到nvcc编译器，请确保CUDA已正确安装"
    exit 1
fi

# 获取GPU架构
GPU_ARCH="sm_60"  # 默认为Pascal架构 (适配Tesla P100)
echo "检测GPU架构..."

# 尝试自动检测GPU架构
if command -v nvidia-smi &> /dev/null; then
    GPU_NAME=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)
    echo "检测到GPU: $GPU_NAME"
    
    # 根据GPU名称设置架构
    if [[ $GPU_NAME == *"P100"* ]]; then
        GPU_ARCH="sm_60"  # Pascal P100
        echo "设置架构为: Pascal P100 (sm_60)"
    elif [[ $GPU_NAME == *"RTX 40"* ]] || [[ $GPU_NAME == *"RTX 4"* ]]; then
        GPU_ARCH="sm_89"  # Ada Lovelace
        echo "设置架构为: Ada Lovelace (sm_89)"
    elif [[ $GPU_NAME == *"RTX 30"* ]] || [[ $GPU_NAME == *"RTX 3"* ]] || [[ $GPU_NAME == *"A100"* ]]; then
        GPU_ARCH="sm_86"  # Ampere
        echo "设置架构为: Ampere (sm_86)"
    elif [[ $GPU_NAME == *"RTX 20"* ]] || [[ $GPU_NAME == *"RTX 2"* ]] || [[ $GPU_NAME == *"GTX 16"* ]]; then
        GPU_ARCH="sm_75"  # Turing
        echo "设置架构为: Turing (sm_75)"
    elif [[ $GPU_NAME == *"GTX 10"* ]] || [[ $GPU_NAME == *"GTX 1"* ]]; then
        GPU_ARCH="sm_61"  # Pascal
        echo "设置架构为: Pascal (sm_61)"
    else
        echo "使用默认架构: Pascal P100 (sm_60)"
    fi
else
    echo "无法检测GPU，使用默认架构: sm_60"
fi

# 编译选项 - 针对性能优化
NVCC_FLAGS="-O3 -arch=$GPU_ARCH -std=c++14 -Xcompiler -fopenmp -use_fast_math -maxrregcount=64"
LIBRARIES="-lcudart"

echo ""
echo "=== 开始编译 ==="
echo "编译命令: nvcc $NVCC_FLAGS sddmm_turbo_optimized.cu $LIBRARIES -o sddmm_turbo"

# 编译
nvcc $NVCC_FLAGS sddmm_turbo_optimized.cu $LIBRARIES -o sddmm_turbo

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo ""
    
    # 检查是否提供了矩阵文件参数
    if [ $# -eq 0 ]; then
        echo "=== 涡轮增压版本特性 ==="
        echo "🚀 针对Tesla P100优化的高性能SDDMM实现"
        echo ""
        echo "主要优化:"
        echo "  1. 减小chunk size (256→128) 提高并行度"
        echo "  2. 优化block size以提高occupancy"
        echo "  3. 批量处理非零元素减少开销"
        echo "  4. 增强的循环展开和指令优化"
        echo "  5. 针对P100的寄存器使用优化"
        echo ""
        echo "预期性能提升: 15-25% (相比原始版本)"
        echo ""
        echo "运行方式: ./sddmm_turbo <matrix_file.mtx> [K]"
        echo ""
        echo "示例:"
        echo "  ./sddmm_turbo matrix.mtx 128"
        echo "  ./sddmm_turbo large_matrix.mtx 256"
    else
        echo "=== 运行涡轮增压版本 ==="
        echo "运行命令: ./sddmm_turbo $@"
        echo ""
        ./sddmm_turbo "$@"
    fi
else
    echo "❌ 编译失败！"
    echo ""
    echo "可能的解决方案:"
    echo "1. 检查CUDA版本是否兼容 (建议CUDA 10.0+)"
    echo "2. 检查GPU架构设置是否正确"
    echo "3. 确保安装了必要的开发库"
    echo ""
    echo "手动编译命令:"
    echo "nvcc -O3 -arch=sm_60 -std=c++14 -Xcompiler -fopenmp -use_fast_math sddmm_turbo_optimized.cu -lcudart -o sddmm_turbo"
    exit 1
fi
