#!/bin/bash

echo "========================================"
echo "   SDDMM 革命性优化修正版编译脚本"
echo "========================================"

# 检查CUDA环境
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到nvcc编译器，请确保CUDA已正确安装"
    exit 1
fi

echo "正在编译SDDMM革命性优化修正版..."

# 编译修正版本
echo "[1/1] 编译革命性优化修正版..."
nvcc -o sddmm_revolutionary_corrected sddmm_revolutionary_corrected.cu -O3 -arch=sm_60 -std=c++11 -Xcompiler "-fopenmp"
if [ $? -ne 0 ]; then
    echo "编译失败，尝试不使用OpenMP..."
    nvcc -o sddmm_revolutionary_corrected sddmm_revolutionary_corrected.cu -O3 -arch=sm_60 -std=c++11
    if [ $? -ne 0 ]; then
        echo "编译失败，尝试使用sm_50架构..."
        nvcc -o sddmm_revolutionary_corrected sddmm_revolutionary_corrected.cu -O3 -arch=sm_50 -std=c++11
        if [ $? -ne 0 ]; then
            echo "编译失败"
            exit 1
        fi
    fi
fi

echo ""
echo "✅ 编译成功！"
echo ""
echo "可执行文件: sddmm_revolutionary_corrected"
echo ""
echo "🚀 革命性优化特性 (修正版):"
echo "  1. 内存层次结构重新设计 - 修正了计算逻辑错误"
echo "  2. 动态负载均衡优化 - 修正了索引访问问题"
echo "  3. 自适应算法选择框架 - 修正了稀疏度计算溢出"
echo ""
echo "🔧 修正的关键问题:"
echo "  - 修正了稀疏度计算溢出 (使用double)"
echo "  - 修正了核函数计算逻辑错误"
echo "  - 修正了内存访问越界问题"
echo "  - 简化了复杂的缓存策略"
echo ""

# 询问是否运行测试
read -p "是否运行测试程序? (y/n): " choice
if [[ $choice == "y" || $choice == "Y" ]]; then
    echo ""
    echo "🚀 运行革命性优化测试..."
    echo "========================================"
    
    # 检查是否有测试矩阵文件
    if [ -f "test_matrix.mtx" ]; then
        echo "使用 test_matrix.mtx 进行测试..."
        ./sddmm_revolutionary_corrected test_matrix.mtx 128
    else
        echo "未找到 test_matrix.mtx，请提供矩阵文件"
        echo "用法: ./sddmm_revolutionary_corrected <matrix_file.mtx> [K=128]"
    fi
    
    echo "========================================"
    echo "测试完成！"
else
    echo ""
    echo "💡 使用方法:"
    echo "  ./sddmm_revolutionary_corrected <matrix_file.mtx> [K]"
    echo ""
    echo "示例:"
    echo "  ./sddmm_revolutionary_corrected test_matrix.mtx 128"
    echo "  ./sddmm_revolutionary_corrected large_matrix.mtx 256"
fi

echo ""
echo "📚 修正版优化策略详解:"
echo ""
echo "🧠 内存层次结构重新设计 (修正版):"
echo "  - 简化的共享内存缓存策略"
echo "  - 修正的warp内归约实现"
echo "  - 正确的内存访问模式"
echo "  - 适用: K>64, 所有稀疏模式"
echo ""
echo "⚖️ 动态负载均衡优化 (修正版):"
echo "  - 修正的原子操作工作分配"
echo "  - 正确的索引计算"
echo "  - 标量计算避免向量化问题"
echo "  - 适用: 所有负载不均衡情况"
echo ""
echo "🎯 自适应算法选择框架 (修正版):"
echo "  - 修正的稀疏度计算 (避免溢出)"
echo "  - 正确的矩阵特征分析"
echo "  - 智能策略选择"
echo "  - 适用: 所有场景的智能优化"
echo ""
echo "🔥 预期性能提升: 2-4倍 (修正版，确保正确性)"
echo ""

echo "程序编译完成！现在应该能够正确计算结果。"
