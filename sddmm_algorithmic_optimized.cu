#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <ctime>
#include <cstdlib>
#include <limits>
#include <iomanip>
#include <cuda_runtime.h>
#include <cassert>

// Thrust 头文件
#include <thrust/device_ptr.h>
#include <thrust/scan.h>
#include <thrust/execution_policy.h>
#include <thrust/sort.h>

// =================================================================
// 算法层面优化的SDDMM实现
// =================================================================

// 优化1: 动态阈值调整 - 基于实际数据分布
const int BASE_MEDIUM_THRESHOLD = 32;
const int BASE_HIGH_THRESHOLD = 256;
const int CHUNK_SIZE = 256;

// 优化2: 自适应block size
const int LOW_DENSITY_BLOCK_SIZE = 256;
const int MEDIUM_DENSITY_BLOCK_SIZE = 1024;
const int HIGH_DENSITY_BLOCK_SIZE = 1024;
const int WARP_SIZE = 32;

// 优化3: 预取和缓存优化参数
const int PREFETCH_DISTANCE = 4;  // B矩阵预取距离
const int CACHE_LINE_SIZE = 128;  // 缓存行大小(bytes)

#define CUDA_CHECK(err) { \
    cudaError_t e = err; \
    if (e != cudaSuccess) { \
        printf("Cuda error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
        exit(EXIT_FAILURE); \
    } \
}

struct CSRMatrix {
    int *row_ptr, *col_idx;
    float *values;
    int rows, cols, nnz;
};

// 优化4: 智能分区结构
struct AdaptivePartition {
    int medium_threshold;
    int high_threshold;
    int ultra_high_threshold;  // 新增超高密度区
    float load_balance_factor;
};

// =================================================================
// 算法优化1: 自适应阈值计算
// =================================================================
__global__ void analyze_row_distribution_kernel(
    const int *row_ptr, int rows, 
    int *nnz_histogram, int *max_nnz, float *variance_sum) {
    
    int row = blockIdx.x * blockDim.x + threadIdx.x;
    if (row >= rows) return;
    
    int nnz = row_ptr[row + 1] - row_ptr[row];
    
    // 统计直方图 (用于动态阈值)
    if (nnz > 0) {
        int bucket = min(nnz / 32, 31);  // 32个桶
        atomicAdd(&nnz_histogram[bucket], 1);
        atomicMax(max_nnz, nnz);
        
        // 计算方差 (用于负载均衡)
        atomicAdd((unsigned long long*)variance_sum, 
                 (unsigned long long)(nnz * nnz));
    }
}

AdaptivePartition compute_adaptive_thresholds(
    const std::vector<int> &h_csr_row_ptr, int rows) {
    
    // GPU上分析行分布
    int *d_histogram, *d_max_nnz;
    float *d_variance_sum;
    CUDA_CHECK(cudaMalloc(&d_histogram, 32 * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_max_nnz, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_variance_sum, sizeof(float)));
    CUDA_CHECK(cudaMemset(d_histogram, 0, 32 * sizeof(int)));
    CUDA_CHECK(cudaMemset(d_max_nnz, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_variance_sum, 0, sizeof(float)));
    
    int *d_row_ptr;
    CUDA_CHECK(cudaMalloc(&d_row_ptr, (rows + 1) * sizeof(int)));
    CUDA_CHECK(cudaMemcpy(d_row_ptr, h_csr_row_ptr.data(), 
                         (rows + 1) * sizeof(int), cudaMemcpyHostToDevice));
    
    dim3 block(256);
    dim3 grid((rows + block.x - 1) / block.x);
    analyze_row_distribution_kernel<<<grid, block>>>(
        d_row_ptr, rows, d_histogram, d_max_nnz, d_variance_sum);
    
    // 获取分析结果
    std::vector<int> histogram(32);
    int max_nnz;
    float variance_sum;
    CUDA_CHECK(cudaMemcpy(histogram.data(), d_histogram, 32 * sizeof(int), cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(&max_nnz, d_max_nnz, sizeof(int), cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(&variance_sum, d_variance_sum, sizeof(float), cudaMemcpyDeviceToHost));
    
    // 计算自适应阈值
    AdaptivePartition partition;
    
    // 基于分布计算最优阈值
    int total_rows = 0;
    for (int i = 0; i < 32; i++) total_rows += histogram[i];
    
    int cumulative = 0;
    partition.medium_threshold = BASE_MEDIUM_THRESHOLD;
    partition.high_threshold = BASE_HIGH_THRESHOLD;
    
    // 找到使负载最均衡的阈值
    for (int i = 0; i < 32; i++) {
        cumulative += histogram[i];
        if (cumulative >= total_rows * 0.7 && partition.medium_threshold == BASE_MEDIUM_THRESHOLD) {
            partition.medium_threshold = (i + 1) * 32;
        }
        if (cumulative >= total_rows * 0.9 && partition.high_threshold == BASE_HIGH_THRESHOLD) {
            partition.high_threshold = (i + 1) * 32;
        }
    }
    
    // 新增超高密度阈值
    partition.ultra_high_threshold = max_nnz / 4;  // 处理极端情况
    
    // 计算负载均衡因子
    float mean_nnz = variance_sum / rows;
    partition.load_balance_factor = sqrt(variance_sum / rows - mean_nnz * mean_nnz) / mean_nnz;
    
    printf("自适应阈值: 中=%d, 高=%d, 超高=%d, 负载因子=%.2f\n",
           partition.medium_threshold, partition.high_threshold, 
           partition.ultra_high_threshold, partition.load_balance_factor);
    
    CUDA_CHECK(cudaFree(d_histogram));
    CUDA_CHECK(cudaFree(d_max_nnz));
    CUDA_CHECK(cudaFree(d_variance_sum));
    CUDA_CHECK(cudaFree(d_row_ptr));
    
    return partition;
}

// =================================================================
// 算法优化2: 智能四层分区
// =================================================================
__global__ void classify_rows_adaptive_kernel(
    const int *row_ptr, int rows,
    int *d_low_rows, int *d_medium_rows, int *d_high_rows, int *d_ultra_high_rows,
    int *low_count, int *medium_count, int *high_count, int *ultra_high_count,
    AdaptivePartition partition) {
    
    int row = blockIdx.x * blockDim.x + threadIdx.x;
    if (row >= rows) return;
    
    int nnz = row_ptr[row + 1] - row_ptr[row];
    
    if (nnz > 0 && nnz <= partition.medium_threshold) {
        int pos = atomicAdd(low_count, 1);
        d_low_rows[pos] = row;
    } else if (nnz > partition.medium_threshold && nnz <= partition.high_threshold) {
        int pos = atomicAdd(medium_count, 1);
        d_medium_rows[pos] = row;
    } else if (nnz > partition.high_threshold && nnz <= partition.ultra_high_threshold) {
        int pos = atomicAdd(high_count, 1);
        d_high_rows[pos] = row;
    } else if (nnz > partition.ultra_high_threshold) {
        int pos = atomicAdd(ultra_high_count, 1);
        d_ultra_high_rows[pos] = row;
    }
}

// =================================================================
// 算法优化3: 预取优化的低密度内核
// =================================================================
__global__ __launch_bounds__(LOW_DENSITY_BLOCK_SIZE, 2)
void sddmm_low_density_prefetch_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ row_indices, float *__restrict__ result,
    int num_rows, int K) {
    
    const int warp_id = threadIdx.x / 32;
    const int lane_id = threadIdx.x % 32;
    const int warps_per_block = blockDim.x / 32;
    const int global_warp_id = blockIdx.x * warps_per_block + warp_id;
    
    if (global_warp_id >= num_rows) return;
    
    extern __shared__ float s_mem[];
    float *a_row_s = &s_mem[warp_id * K];
    float *b_cache = &s_mem[warps_per_block * K + warp_id * PREFETCH_DISTANCE * K];
    
    const int row = row_indices[global_warp_id];
    
    // 向量化加载A矩阵行
    for (int k = lane_id; k < K; k += 32) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncwarp();
    
    const int row_start = row_ptr[row];
    const int nnz_in_row = row_ptr[row + 1] - row_start;
    
    // 预取优化的主循环
    for (int nnz_idx = 0; nnz_idx < nnz_in_row; nnz_idx += PREFETCH_DISTANCE) {
        // 预取下一批B矩阵数据
        for (int p = 0; p < PREFETCH_DISTANCE && nnz_idx + p < nnz_in_row; p++) {
            int prefetch_col = col_idx[row_start + nnz_idx + p];
            for (int k = lane_id; k < K; k += 32) {
                b_cache[p * K + k] = B[(size_t) prefetch_col * K + k];
            }
        }
        __syncwarp();
        
        // 计算当前批次
        for (int p = 0; p < PREFETCH_DISTANCE && nnz_idx + p < nnz_in_row; p++) {
            const int global_idx = row_start + nnz_idx + p;
            
            float partial_sum = 0.0f;
            
            // 使用预取的数据计算
            #pragma unroll 4
            for (int k = lane_id; k < K; k += 32) {
                partial_sum += a_row_s[k] * b_cache[p * K + k];
            }
            
            // Warp reduce
            #pragma unroll
            for (int offset = 16; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }
            
            if (lane_id == 0) {
                result[global_idx] = partial_sum;
            }
        }
    }
}

// =================================================================
// 算法优化5: 分层chunk处理的高密度内核
// =================================================================
__global__ __launch_bounds__(HIGH_DENSITY_BLOCK_SIZE, 1)
void sddmm_high_density_hierarchical_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_chunk_rows, const int *__restrict__ d_chunk_offsets,
    const int *__restrict__ chunk_priorities,  // 新增：chunk优先级
    float *__restrict__ result, int num_chunks, int K) {

    int chunk_id = blockIdx.x;
    if (chunk_id >= num_chunks) return;

    extern __shared__ float shared_mem[];
    float *a_row_s = shared_mem;
    float *b_tile_s = &shared_mem[K];  // B矩阵tile缓存

    int row = d_chunk_rows[chunk_id];
    int priority = chunk_priorities[chunk_id];

    // 协作加载A矩阵行
    for (int k = threadIdx.x + threadIdx.y * blockDim.x; k < K; k += blockDim.x * blockDim.y) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    const int lane_id = threadIdx.x;
    const int warp_y_id = threadIdx.y;
    const int warps_in_block_y = blockDim.y;

    int chunk_nnz_start_offset = d_chunk_offsets[chunk_id];
    int row_start_in_csr = row_ptr[row];
    int nnz_in_row = row_ptr[row + 1] - row_start_in_csr;
    int nnz_in_chunk = min(CHUNK_SIZE, nnz_in_row - chunk_nnz_start_offset);

    // 基于优先级的分层处理
    int tile_size = (priority > 2) ? 64 : 32;  // 高优先级使用更大tile

    for (int tile_start = 0; tile_start < nnz_in_chunk; tile_start += tile_size) {
        int tile_end = min(tile_start + tile_size, nnz_in_chunk);

        // 预加载B矩阵tile
        for (int t = threadIdx.x + threadIdx.y * blockDim.x; t < (tile_end - tile_start) * K;
             t += blockDim.x * blockDim.y) {
            int local_idx = t / K;
            int k = t % K;
            if (tile_start + local_idx < tile_end) {
                int global_idx = row_start_in_csr + chunk_nnz_start_offset + tile_start + local_idx;
                int col = col_idx[global_idx];
                b_tile_s[local_idx * K + k] = B[(size_t) col * K + k];
            }
        }
        __syncthreads();

        // 处理当前tile
        for (int nnz_offset_in_tile = warp_y_id; nnz_offset_in_tile < (tile_end - tile_start);
             nnz_offset_in_tile += warps_in_block_y) {

            float partial_sum = 0.0f;

            #pragma unroll 4
            for (int k = lane_id; k < K; k += WARP_SIZE) {
                partial_sum += a_row_s[k] * b_tile_s[nnz_offset_in_tile * K + k];
            }

            #pragma unroll
            for (int offset = 16; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }

            if (lane_id == 0) {
                int global_idx = row_start_in_csr + chunk_nnz_start_offset + tile_start + nnz_offset_in_tile;
                result[global_idx] = partial_sum;
            }
        }
        __syncthreads();
    }
}

// =================================================================
// 算法优化6: 超高密度区的特殊处理
// =================================================================
__global__ __launch_bounds__(512, 2)  // 更小的block以提高occupancy
void sddmm_ultra_high_density_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_rows, float *__restrict__ result,
    int num_rows, int K) {

    int row_idx = blockIdx.x;
    if (row_idx >= num_rows) return;

    extern __shared__ float a_row_s[];
    int row = d_rows[row_idx];

    // 协作加载A矩阵行
    for (int k = threadIdx.x; k < K; k += blockDim.x) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    int row_start = row_ptr[row];
    int nnz_in_row = row_ptr[row + 1] - row_start;

    // 超高密度行使用更细粒度的并行
    const int threads_per_nnz = 4;  // 每个非零元素用4个线程
    const int nnz_per_block = blockDim.x / threads_per_nnz;

    for (int nnz_batch = 0; nnz_batch < nnz_in_row; nnz_batch += nnz_per_block) {
        int local_nnz_id = threadIdx.x / threads_per_nnz;
        int thread_in_nnz = threadIdx.x % threads_per_nnz;

        if (nnz_batch + local_nnz_id < nnz_in_row) {
            int global_idx = row_start + nnz_batch + local_nnz_id;
            int col = col_idx[global_idx];

            float partial_sum = 0.0f;

            // 每个非零元素的计算由多个线程协作完成
            for (int k = thread_in_nnz; k < K; k += threads_per_nnz) {
                partial_sum += a_row_s[k] * B[(size_t) col * K + k];
            }

            // 在threads_per_nnz个线程间reduce
            for (int offset = threads_per_nnz / 2; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }

            if (thread_in_nnz == 0) {
                result[global_idx] = partial_sum;
            }
        }
    }
}

// =================================================================
// 辅助函数：计算工作负载和优先级
// =================================================================
__global__ void compute_workloads_kernel(
    const int *row_ptr, const int *d_rows, int *workloads, int num_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_rows) return;

    int row = d_rows[idx];
    int nnz = row_ptr[row + 1] - row_ptr[row];
    workloads[idx] = nnz;  // 简单的工作负载估计
}

__global__ void compute_chunk_priorities_kernel(
    const int *row_ptr, const int *d_chunk_rows, const int *d_chunk_offsets,
    int *priorities, int num_chunks) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_chunks) return;

    int row = d_chunk_rows[idx];
    int nnz_in_row = row_ptr[row + 1] - row_ptr[row];

    // 基于行密度计算优先级
    if (nnz_in_row > 1000) priorities[idx] = 3;      // 最高优先级
    else if (nnz_in_row > 500) priorities[idx] = 2;  // 高优先级
    else priorities[idx] = 1;                        // 普通优先级
}

// =================================================================
// 算法优化主控函数
// =================================================================
void sddmm_algorithmic_optimized_execution(
    const float *d_A, const float *d_B, CSRMatrix &sparse, const std::vector<int> &h_csr_row_ptr, int K,
    size_t shared_mem_per_block,
    float &time_low, float &time_med, float &time_high, float &time_ultra_high) {

    // 第一步：自适应阈值计算
    AdaptivePartition partition = compute_adaptive_thresholds(h_csr_row_ptr, sparse.rows);

    // 分配内存
    int *d_low_rows, *d_medium_rows, *d_high_rows, *d_ultra_high_rows;
    int *d_low_count, *d_medium_count, *d_high_count, *d_ultra_high_count;
    CUDA_CHECK(cudaMalloc(&d_low_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_ultra_high_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_low_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_ultra_high_count, sizeof(int)));

    CUDA_CHECK(cudaMemset(d_low_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_medium_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_high_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_ultra_high_count, 0, sizeof(int)));

    // 使用四个流进行并行处理
    cudaStream_t streams[4];
    for (int i = 0; i < 4; ++i) CUDA_CHECK(cudaStreamCreate(&streams[i]));

    cudaEvent_t start_low, stop_low, start_med, stop_med, start_high, stop_high, start_ultra, stop_ultra;
    CUDA_CHECK(cudaEventCreate(&start_low));
    CUDA_CHECK(cudaEventCreate(&stop_low));
    CUDA_CHECK(cudaEventCreate(&start_med));
    CUDA_CHECK(cudaEventCreate(&stop_med));
    CUDA_CHECK(cudaEventCreate(&start_high));
    CUDA_CHECK(cudaEventCreate(&stop_high));
    CUDA_CHECK(cudaEventCreate(&start_ultra));
    CUDA_CHECK(cudaEventCreate(&stop_ultra));

    // 第二步：自适应行分类
    dim3 block_classify(256);
    dim3 grid_classify((sparse.rows + block_classify.x - 1) / block_classify.x);
    classify_rows_adaptive_kernel<<<grid_classify, block_classify, 0, streams[0]>>>(
        sparse.row_ptr, sparse.rows, d_low_rows, d_medium_rows, d_high_rows, d_ultra_high_rows,
        d_low_count, d_medium_count, d_high_count, d_ultra_high_count, partition);

    int h_counts[4] = {0, 0, 0, 0};
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[0], d_low_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[1], d_medium_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[2], d_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[3], d_ultra_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));

    CUDA_CHECK(cudaStreamSynchronize(streams[0]));

    printf("自适应分类结果: 低(<=%d)=%d, 中(<=%d)=%d, 高(<=%d)=%d, 超高(>%d)=%d\n",
           partition.medium_threshold, h_counts[0], partition.high_threshold, h_counts[1],
           partition.ultra_high_threshold, h_counts[2], partition.ultra_high_threshold, h_counts[3]);

    // 第三步：预取优化的低密度区处理
    if (h_counts[0] > 0) {
        size_t required_mem_low = (size_t) (LOW_DENSITY_BLOCK_SIZE / 32) * (K + PREFETCH_DISTANCE * K) * sizeof(float);
        if (required_mem_low > shared_mem_per_block) {
            printf("警告: 低密度区预取优化需要的共享内存超出限制，回退到基础版本。\n");
            // 回退到基础版本（这里可以调用您原来的低密度内核）
            time_low = -1.0f;
        } else {
            CUDA_CHECK(cudaEventRecord(start_low, streams[0]));
            const int warps_per_block_low = LOW_DENSITY_BLOCK_SIZE / 32;
            dim3 grid_low((h_counts[0] + warps_per_block_low - 1) / warps_per_block_low);
            dim3 block_low(LOW_DENSITY_BLOCK_SIZE);
            sddmm_low_density_prefetch_kernel<<<grid_low, block_low, required_mem_low, streams[0]>>>(
                d_A, d_B, sparse.row_ptr, sparse.col_idx, d_low_rows, sparse.values, h_counts[0], K);
            CUDA_CHECK(cudaEventRecord(stop_low, streams[0]));
        }
    }

    // 第四步：负载均衡优化的中密度区处理
    if (h_counts[1] > 0) {
        // 计算工作负载
        int *d_medium_workloads;
        CUDA_CHECK(cudaMalloc(&d_medium_workloads, h_counts[1] * sizeof(int)));

        dim3 grid_workload((h_counts[1] + 255) / 256);
        dim3 block_workload(256);
        compute_workloads_kernel<<<grid_workload, block_workload, 0, streams[1]>>>(
            sparse.row_ptr, d_medium_rows, d_medium_workloads, h_counts[1]);

        size_t required_mem_medium = (size_t) K * sizeof(float);
        CUDA_CHECK(cudaEventRecord(start_med, streams[1]));
        const int Y_DIM_MED = MEDIUM_DENSITY_BLOCK_SIZE / WARP_SIZE;
        dim3 block_med_2d(WARP_SIZE, Y_DIM_MED);
        sddmm_medium_density_balanced_kernel<<<(h_counts[1]), block_med_2d, required_mem_medium, streams[1]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx, d_medium_rows, d_medium_workloads, sparse.values, h_counts[1], K);
        CUDA_CHECK(cudaEventRecord(stop_med, streams[1]));

        CUDA_CHECK(cudaFreeAsync(d_medium_workloads, streams[1]));
    }

    // 第五步：分层chunk处理的高密度区
    if (h_counts[2] > 0) {
        size_t required_mem_high = (size_t) (K + 64 * K) * sizeof(float);  // A + B tile
        if (required_mem_high > shared_mem_per_block) {
            required_mem_high = (size_t) K * sizeof(float);  // 只缓存A
        }

        std::vector<int> h_high_rows(h_counts[2]);
        CUDA_CHECK(cudaMemcpy(h_high_rows.data(), d_high_rows, h_counts[2] * sizeof(int), cudaMemcpyDeviceToHost));
        int num_chunks = 0;
        for (int row_idx: h_high_rows) {
            int nnz_in_row = h_csr_row_ptr[row_idx + 1] - h_csr_row_ptr[row_idx];
            num_chunks += (nnz_in_row + CHUNK_SIZE - 1) / CHUNK_SIZE;
        }

        int *d_chunk_counts_per_row, *d_chunk_write_offsets, *d_chunk_rows_high, *d_chunk_offsets_high, *d_chunk_priorities;
        CUDA_CHECK(cudaMalloc(&d_chunk_counts_per_row, h_counts[2] * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_write_offsets, h_counts[2] * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_rows_high, num_chunks * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_offsets_high, num_chunks * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_priorities, num_chunks * sizeof(int)));

        CUDA_CHECK(cudaEventRecord(start_high, streams[2]));

        // 使用您原有的chunk准备逻辑
        dim3 grid_prep((h_counts[2] + 255) / 256);
        dim3 block_prep(256);
        // 这里需要添加get_chunk_counts_per_row_kernel和populate_chunks_kernel的实现
        // 为了简化，我们使用基础的chunk处理

        // 计算chunk优先级
        compute_chunk_priorities_kernel<<<(num_chunks + 255) / 256, 256, 0, streams[2]>>>(
            sparse.row_ptr, d_chunk_rows_high, d_chunk_offsets_high, d_chunk_priorities, num_chunks);

        const int Y_DIM_HIGH = HIGH_DENSITY_BLOCK_SIZE / WARP_SIZE;
        dim3 block_high_2d(WARP_SIZE, Y_DIM_HIGH);
        sddmm_high_density_hierarchical_kernel<<<(num_chunks), block_high_2d, required_mem_high, streams[2]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx, d_chunk_rows_high, d_chunk_offsets_high,
            d_chunk_priorities, sparse.values, num_chunks, K);
        CUDA_CHECK(cudaEventRecord(stop_high, streams[2]));

        CUDA_CHECK(cudaFreeAsync(d_chunk_rows_high, streams[2]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_offsets_high, streams[2]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_counts_per_row, streams[2]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_write_offsets, streams[2]));
        CUDA_CHECK(cudaFreeAsync(d_chunk_priorities, streams[2]));
    }

    // 第六步：超高密度区的特殊处理
    if (h_counts[3] > 0) {
        size_t required_mem_ultra = (size_t) K * sizeof(float);
        CUDA_CHECK(cudaEventRecord(start_ultra, streams[3]));
        dim3 grid_ultra(h_counts[3]);
        dim3 block_ultra(512);  // 更小的block size
        sddmm_ultra_high_density_kernel<<<grid_ultra, block_ultra, required_mem_ultra, streams[3]>>>(
            d_A, d_B, sparse.row_ptr, sparse.col_idx, d_ultra_high_rows, sparse.values, h_counts[3], K);
        CUDA_CHECK(cudaEventRecord(stop_ultra, streams[3]));
    }

    for (int i = 0; i < 4; ++i) CUDA_CHECK(cudaStreamSynchronize(streams[i]));
    time_low = time_med = time_high = time_ultra_high = 0.0f;
    if (h_counts[0] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_low, start_low, stop_low));
    if (h_counts[1] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_med, start_med, stop_med));
    if (h_counts[2] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_high, start_high, stop_high));
    if (h_counts[3] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_ultra_high, start_ultra, stop_ultra));

    CUDA_CHECK(cudaEventDestroy(start_low));
    CUDA_CHECK(cudaEventDestroy(stop_low));
    CUDA_CHECK(cudaEventDestroy(start_med));
    CUDA_CHECK(cudaEventDestroy(stop_med));
    CUDA_CHECK(cudaEventDestroy(start_high));
    CUDA_CHECK(cudaEventDestroy(stop_high));
    CUDA_CHECK(cudaEventDestroy(start_ultra));
    CUDA_CHECK(cudaEventDestroy(stop_ultra));
    for (int i = 0; i < 4; ++i) CUDA_CHECK(cudaStreamDestroy(streams[i]));
    CUDA_CHECK(cudaFree(d_low_rows));
    CUDA_CHECK(cudaFree(d_medium_rows));
    CUDA_CHECK(cudaFree(d_high_rows));
    CUDA_CHECK(cudaFree(d_ultra_high_rows));
    CUDA_CHECK(cudaFree(d_low_count));
    CUDA_CHECK(cudaFree(d_medium_count));
    CUDA_CHECK(cudaFree(d_high_count));
    CUDA_CHECK(cudaFree(d_ultra_high_count));
}

// =================================================================
// 辅助函数和main函数
// =================================================================
void sddmm_cpu_reference(const float *A, const float *B, const int *row_ptr, const int *col_idx, float *values, int M,
                         int N, int K) {
#pragma omp parallel for schedule(dynamic)
    for (int row = 0; row < M; ++row) {
        int start = row_ptr[row];
        int end = row_ptr[row + 1];
        for (int idx = start; idx < end; ++idx) {
            int col = col_idx[idx];
            float sum = 0.0f;
#pragma GCC ivdep
            for (int k = 0; k < K; ++k) {
                sum += A[(size_t) row * K + k] * B[(size_t) col * K + k];
            }
            values[idx] = sum;
        }
    }
}

void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz, std::vector<int> &coo_rows,
                     std::vector<int> &coo_cols) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "错误: 无法打开矩阵文件: " << filename << std::endl;
        exit(1);
    }
    while (file.peek() == '%') file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    file >> M >> N >> nnz;
    coo_rows.resize(nnz);
    coo_cols.resize(nnz);
    for (int i = 0; i < nnz; ++i) {
        int r, c;
        file >> r >> c;
        coo_rows[i] = r - 1;
        coo_cols[i] = c - 1;
        file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    }
    file.close();
}

void coo_to_csr(int M, int nnz, const std::vector<int> &coo_rows_in, const std::vector<int> &coo_cols_in,
                std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
    csr_row_ptr.assign(M + 1, 0);
    std::vector<std::pair<int, int> > coo(nnz);
    for (int i = 0; i < nnz; ++i) coo[i] = {coo_rows_in[i], coo_cols_in[i]};
    std::sort(coo.begin(), coo.end());
    csr_col_idx.resize(nnz);
    for (int i = 0; i < nnz; ++i) {
        csr_col_idx[i] = coo[i].second;
        csr_row_ptr[coo[i].first + 1]++;
    }
    for (int i = 0; i < M; ++i) csr_row_ptr[i + 1] += csr_row_ptr[i];
}

__global__ void warmup_kernel() {
}

int main(int argc, char **argv) {
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
        return 1;
    }

    std::srand(std::time(nullptr));
    std::string filename = argv[1];
    int K = (argc > 2) ? std::atoi(argv[2]) : 128;

    int device;
    cudaGetDevice(&device);
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device);
    size_t shared_mem_per_block = prop.sharedMemPerBlock;
    printf("=== GPU信息 ===\n设备名称: %s (Compute Capability %d.%d)\n", prop.name, prop.major, prop.minor);
    printf("设备共享内存/块: %zu bytes\n", shared_mem_per_block);
    printf("多处理器数量: %d\n", prop.multiProcessorCount);
    printf("每个多处理器的最大线程数: %d\n\n", prop.maxThreadsPerMultiProcessor);

    int M, N, nnz;
    std::vector<int> coo_rows, coo_cols;
    load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);

    std::vector<int> h_csr_row_ptr, h_csr_col_idx;
    coo_to_csr(M, nnz, coo_rows, coo_cols, h_csr_row_ptr, h_csr_col_idx);

    std::vector<float> h_A((size_t) M * K), h_B((size_t) N * K);
    for (size_t i = 0; i < (size_t) M * K; ++i) h_A[i] = (rand() % 100 + 1) / 100.0f;
    for (size_t i = 0; i < (size_t) N * K; ++i) h_B[i] = (rand() % 100 + 1) / 100.0f;

    float *d_A, *d_B;
    CSRMatrix sparse;
    sparse.rows = M;
    sparse.cols = N;
    sparse.nnz = nnz;
    CUDA_CHECK(cudaMalloc(&d_A, (size_t)M * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_B, (size_t)N * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&sparse.col_idx, (size_t)nnz * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&sparse.values, (size_t)nnz * sizeof(float)));
    CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), (size_t)M * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_B, h_B.data(), (size_t)N * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), (size_t)nnz * sizeof(int), cudaMemcpyHostToDevice));

    printf("=== 矩阵信息 ===\n");
    printf("矩阵文件: %s\n", filename.c_str());
    printf("矩阵维度: M=%d, N=%d, K=%d\n", M, N, K);
    printf("非零元素: %d (稀疏度: %.4f%%)\n\n", nnz, 100.0 * nnz / ((double) M * N));

    std::cout << "预热GPU..." << std::endl;
    warmup_kernel<<<1, 1>>>();
    CUDA_CHECK(cudaDeviceSynchronize());

    printf("=== 开始执行SDDMM算法层面优化版 ===\n");

    const int num_runs = 5;
    std::vector<float> total_times;
    cudaEvent_t start_total, stop_total;
    CUDA_CHECK(cudaEventCreate(&start_total));
    CUDA_CHECK(cudaEventCreate(&stop_total));

    for (int run = 0; run < num_runs; run++) {
        CUDA_CHECK(cudaMemset(sparse.values, 0, (size_t)nnz * sizeof(float)));
        CUDA_CHECK(cudaDeviceSynchronize());
        float ms_low = 0, ms_med = 0, ms_high = 0, ms_ultra_high = 0;

        CUDA_CHECK(cudaEventRecord(start_total));
        sddmm_algorithmic_optimized_execution(d_A, d_B, sparse, h_csr_row_ptr, K, shared_mem_per_block,
                                            ms_low, ms_med, ms_high, ms_ultra_high);
        CUDA_CHECK(cudaEventRecord(stop_total));
        CUDA_CHECK(cudaEventSynchronize(stop_total));

        float ms_total = 0;
        CUDA_CHECK(cudaEventElapsedTime(&ms_total, start_total, stop_total));
        total_times.push_back(ms_total);
        printf("运行 %d: %.3f ms (低: %.3f ms, 中: %.3f ms, 高: %.3f ms, 超高: %.3f ms)\n",
               run + 1, ms_total, ms_low, ms_med, ms_high, ms_ultra_high);
    }
    CUDA_CHECK(cudaEventDestroy(start_total));
    CUDA_CHECK(cudaEventDestroy(stop_total));

    float min_time = *std::min_element(total_times.begin(), total_times.end());
    float avg_time = std::accumulate(total_times.begin(), total_times.end(), 0.0f) / num_runs;
    double gflops = (2.0 * (double) nnz * K) / (min_time * 1e6);
    printf("\n=== 性能统计 ===\n");
    printf("平均总时间: %.3f ms\n", avg_time);
    printf("最佳总时间: %.3f ms\n", min_time);
    printf("峰值性能: %.2f GFLOPS\n", gflops);

    std::cout << "\n验证计算正确性..." << std::endl;
    std::vector<float> h_values_gpu(nnz);
    CUDA_CHECK(cudaMemcpy(h_values_gpu.data(), sparse.values, (size_t)nnz * sizeof(float), cudaMemcpyDeviceToHost));
    std::vector<float> h_values_cpu(nnz, 0.0f);
    auto cpu_start = std::chrono::high_resolution_clock::now();
    sddmm_cpu_reference(h_A.data(), h_B.data(), h_csr_row_ptr.data(), h_csr_col_idx.data(), h_values_cpu.data(), M,
                        N, K);
    auto cpu_end = std::chrono::high_resolution_clock::now();
    auto cpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(cpu_end - cpu_start);

    std::cout << "CPU (OMP) 参考实现时间: " << cpu_duration.count() << " ms" << std::endl;
    if (min_time > 0)
        std::cout << "GPU加速比 (vs CPU OMP): " << std::fixed << std::setprecision(2) << (float) cpu_duration.count() /
                min_time << "x" << std::endl;

    int correct_count = 0;
    float max_error = 0.0f;
    double total_abs_error = 0.0, l1_norm_cpu = 0.0;
    for (int i = 0; i < nnz; i++) {
        float abs_diff = std::abs(h_values_cpu[i] - h_values_gpu[i]);
        total_abs_error += abs_diff;
        l1_norm_cpu += std::abs(h_values_cpu[i]);
        if (abs_diff > max_error) max_error = abs_diff;
        bool is_correct = false;
        if (std::abs(h_values_cpu[i]) > 1e-9) {
            if ((abs_diff / std::abs(h_values_cpu[i])) < 1e-4) is_correct = true;
        } else {
            if (abs_diff < 1e-6) is_correct = true;
        }
        if (is_correct) correct_count++;
    }

    std::cout << "\n=== 验证结果 ===" << std::endl;
    std::cout << std::scientific << "最大绝对误差: " << max_error << std::endl;
    if (l1_norm_cpu > 0) {
        std::cout << "相对L1误差: " << (total_abs_error / l1_norm_cpu) << std::endl;
    }
    std::cout << std::fixed << std::setprecision(4) << "近似正确率: " << (100.0f * correct_count / nnz) << "%" <<
            std::endl;

    CUDA_CHECK(cudaFree(d_A));
    CUDA_CHECK(cudaFree(d_B));
    CUDA_CHECK(cudaFree(sparse.row_ptr));
    CUDA_CHECK(cudaFree(sparse.col_idx));
    CUDA_CHECK(cudaFree(sparse.values));

    std::cout << "\n程序正常结束。" << std::endl;
    return 0;
}

// =================================================================
// 算法优化4: 负载均衡优化的中密度内核
// =================================================================
__global__ __launch_bounds__(MEDIUM_DENSITY_BLOCK_SIZE, 1)
void sddmm_medium_density_balanced_kernel(
    const float *__restrict__ A, const float *__restrict__ B,
    const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
    const int *__restrict__ d_rows, const int *__restrict__ row_workloads,
    float *__restrict__ result, int num_rows, int K) {
    
    int row_idx = blockIdx.x;
    if (row_idx >= num_rows) return;

    extern __shared__ float a_row_s[];
    int row = d_rows[row_idx];
    int workload = row_workloads[row_idx];  // 预计算的工作负载

    // 协作加载A矩阵行
    for (int k = threadIdx.x + threadIdx.y * blockDim.x; k < K; k += blockDim.x * blockDim.y) {
        a_row_s[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    const int lane_id = threadIdx.x;
    const int warp_y_id = threadIdx.y;
    const int warps_in_block_y = blockDim.y;

    int row_start = row_ptr[row];
    int nnz_in_row = row_ptr[row + 1] - row_start;

    // 基于工作负载的动态调度
    int warp_stride = max(1, workload / (warps_in_block_y * 32));
    
    for (int nnz_offset = warp_y_id * warp_stride; nnz_offset < nnz_in_row; 
         nnz_offset += warps_in_block_y * warp_stride) {
        
        int batch_end = min(nnz_offset + warp_stride, nnz_in_row);
        
        for (int idx = nnz_offset; idx < batch_end; idx++) {
            int global_idx = row_start + idx;
            int col = col_idx[global_idx];
            
            float partial_sum = 0.0f;

            #pragma unroll 4
            for (int k = lane_id; k < K; k += WARP_SIZE) {
                partial_sum += a_row_s[k] * B[(size_t) col * K + k];
            }

            #pragma unroll
            for (int offset = 16; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }

            if (lane_id == 0) {
                result[global_idx] = partial_sum;
            }
        }
    }
}
