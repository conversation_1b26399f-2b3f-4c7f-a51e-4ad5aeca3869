# SDDMM 高性能优化版本

## 概述

这是一个针对 SDDMM (Sampled Dense-Dense Matrix Multiplication) 的高度优化实现，专门为不支持 Tensor Core 的 GPU 设计。该版本实现了多种先进的优化策略，可以根据矩阵特征自动选择最优算法。

## 主要优化策略

### 1. 🚀 向量化计算优化
- 使用 `float4` 向量化指令
- 优化内存带宽利用率
- 适用于 K 维度为 4 的倍数的情况
- **预期提升**: 20-40%

### 2. ⚖️ 动态负载均衡
- 全局工作队列管理
- 自适应工作块大小
- 解决负载不均衡问题
- **预期提升**: 30-50% (负载不均衡场景)

### 3. 🔄 多流并行处理
- 4 个并行 CUDA 流
- 异步数据传输与计算重叠
- 智能数据分段策略
- **预期提升**: 40-80% (大规模矩阵)

### 4. 💾 内存合并访问优化
- 优化内存访问模式
- 减少内存事务数量
- 提高缓存命中率
- **预期提升**: 15-30%

### 5. 🧠 自适应算法选择
- 运行时性能分析
- 智能策略选择
- 自动适配不同稀疏模式

## 文件结构

```
├── sddmm_optimized_complete.cu    # 主要优化实现
├── compile_optimized.sh           # 编译脚本
├── generate_test_matrix.cpp       # 测试矩阵生成器
├── README_OPTIMIZED.md           # 本文档
└── SDDMM_OPTIMIZATION_STRATEGIES.md  # 详细优化策略说明
```

## 快速开始

### 1. 编译程序

```bash
# 给脚本执行权限
chmod +x compile_optimized.sh

# 编译 (自动检测GPU架构)
./compile_optimized.sh

# 或者手动编译
nvcc -O3 -arch=sm_75 -std=c++14 -Xcompiler -fopenmp sddmm_optimized_complete.cu -lcudart -o sddmm_optimized
```

### 2. 生成测试矩阵 (可选)

```bash
# 编译矩阵生成器
g++ -O3 generate_test_matrix.cpp -o generate_test_matrix

# 生成测试矩阵
./generate_test_matrix test_matrix.mtx 1000 1000 0.05
```

### 3. 运行程序

```bash
# 使用默认K=128
./sddmm_optimized test_matrix.mtx

# 指定K值
./sddmm_optimized test_matrix.mtx 256
```

## 性能特征

### 自动策略选择规则

程序会根据以下特征自动选择最优策略：

1. **向量化计算**: K ≥ 64 且 K 为 4 的倍数
2. **多流并行**: NNZ > 20,000
3. **动态负载均衡**: 负载不均衡因子 > 3.0 且 NNZ > 5,000
4. **内存合并优化**: 规律稀疏模式且平均每行 NNZ > 16
5. **原始优化**: 其他情况

### 预期性能提升

| 矩阵类型 | 预期提升 | 最佳策略 |
|---------|---------|----------|
| 规律稀疏矩阵 | 1.5-2.5x | 向量化计算 |
| 不规律稀疏矩阵 | 2-4x | 动态负载均衡 |
| 大规模矩阵 | 3-6x | 多流并行 |
| 高K维度矩阵 | 2-4x | 向量化计算 |

## 系统要求

### 硬件要求
- NVIDIA GPU (Compute Capability 6.1+)
- 推荐: GTX 1060 或更高
- 内存: 至少 4GB GPU 内存

### 软件要求
- CUDA 11.0 或更高版本
- GCC 7.0 或更高版本
- OpenMP 支持

### 支持的GPU架构
- Pascal (sm_61): GTX 10 系列
- Turing (sm_75): RTX 20 系列, GTX 16 系列
- Ampere (sm_86): RTX 30 系列, A100
- Ada Lovelace (sm_89): RTX 40 系列

## 使用示例

### 基本使用
```bash
./sddmm_optimized matrix.mtx 128
```

### 输出示例
```
=== GPU信息 ===
设备名称: NVIDIA GeForce RTX 3080 (Compute Capability 8.6)
设备共享内存/块: 49152 bytes
设备全局内存: 10.00 GB
多处理器数量: 68

=== 矩阵信息 ===
矩阵文件: test_matrix.mtx
矩阵维度: M=1000, N=1000, K=128
非零元素: 50000 (稀疏度: 5.0000%)

=== 性能分析结果 ===
平均每行NNZ: 50.00
NNZ方差: 45.23
最大每行NNZ: 78
负载不均衡因子: 1.56
是否规律模式: 否

选择策略: 多流并行处理 (NNZ=50000 > 20000)

运行 1: 2.345 ms
运行 2: 2.298 ms
运行 3: 2.312 ms
运行 4: 2.289 ms
运行 5: 2.301 ms

=== 性能统计 ===
平均执行时间: 2.309 ms
最佳执行时间: 2.289 ms
峰值性能: 55.98 GFLOPS

CPU参考实现时间: 145 ms
GPU加速比: 63.35x

=== 验证结果 ===
最大绝对误差: 1.23e-06
相对L1误差: 2.45e-07
正确率: 99.9987%
```

## 故障排除

### 编译错误
1. **CUDA 版本不兼容**: 确保使用 CUDA 11.0+
2. **架构不匹配**: 检查 GPU 架构设置
3. **缺少库文件**: 安装完整的 CUDA 开发工具包

### 运行时错误
1. **内存不足**: 减少 K 值或使用更小的矩阵
2. **共享内存超限**: 程序会自动回退到兼容模式
3. **矩阵格式错误**: 确保使用 Matrix Market 格式

### 性能问题
1. **性能提升不明显**: 检查矩阵规模和稀疏模式
2. **GPU 利用率低**: 尝试增加矩阵规模
3. **内存带宽受限**: 考虑使用更高端的 GPU

## 技术细节

### 核心优化技术
- **Warp-level 原语**: 使用 `__shfl_down_sync` 进行高效归约
- **共享内存优化**: 智能缓存 A 矩阵行数据
- **内存合并**: 优化全局内存访问模式
- **异步执行**: 多流并行隐藏延迟

### 算法复杂度
- **时间复杂度**: O(NNZ × K)
- **空间复杂度**: O(M × K + N × K + NNZ)
- **通信复杂度**: O(NNZ × K) 内存访问

## 贡献与反馈

如果您发现任何问题或有改进建议，请：
1. 检查现有的故障排除指南
2. 提供详细的错误信息和系统配置
3. 包含测试用例和预期结果

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
