@echo off
echo ========================================
echo    SDDMM 革命性优化完整版编译脚本
echo ========================================

REM 检查CUDA环境
where nvcc >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到nvcc编译器，请确保CUDA已正确安装
    pause
    exit /b 1
)

echo 正在编译SDDMM革命性优化完整版...

REM 编译主程序
echo [1/1] 编译革命性优化完整版...
nvcc -o sddmm_revolutionary_complete sddmm_revolutionary_complete.cu -O3 -arch=sm_70 -std=c++11 -Xcompiler "/openmp" -lcudart -lthrust
if %ERRORLEVEL% NEQ 0 (
    echo 编译失败，尝试不使用OpenMP...
    nvcc -o sddmm_revolutionary_complete sddmm_revolutionary_complete.cu -O3 -arch=sm_70 -std=c++11 -lcudart -lthrust
    if %ERRORLEVEL% NEQ 0 (
        echo 编译失败
        goto :error
    )
)

echo.
echo ✅ 编译成功！
echo.
echo 可执行文件: sddmm_revolutionary_complete.exe
echo.
echo 🚀 革命性优化特性:
echo   1. 内存层次结构重新设计 - 三级缓存协同优化
echo   2. 计算与通信重叠流水线 - 异步并行执行
echo   3. 自适应算法选择框架 - 智能策略匹配
echo.

REM 询问是否运行测试
set /p choice="是否运行测试程序? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 运行革命性优化测试...
    echo ========================================
    
    REM 检查是否有测试矩阵文件
    if exist "test_matrix.mtx" (
        echo 使用 test_matrix.mtx 进行测试...
        sddmm_revolutionary_complete.exe test_matrix.mtx 128
    ) else (
        echo 未找到 test_matrix.mtx，请提供矩阵文件
        echo 用法: sddmm_revolutionary_complete.exe ^<matrix_file.mtx^> [K=128]
    )
    
    echo ========================================
    echo 测试完成！
) else (
    echo.
    echo 💡 使用方法:
    echo   sddmm_revolutionary_complete.exe ^<matrix_file.mtx^> [K]
    echo.
    echo 示例:
    echo   sddmm_revolutionary_complete.exe test_matrix.mtx 128
    echo   sddmm_revolutionary_complete.exe large_matrix.mtx 256
)

echo.
echo 📚 优化策略详解:
echo.
echo 🧠 内存层次结构重新设计:
echo   - 三级缓存策略: 寄存器 → 共享内存 → L2缓存
echo   - 缓存感知的数据预取和重排
echo   - 动态负载均衡避免warp分化
echo   - 适用: K^>64, 不规则稀疏模式
echo.
echo 🌊 计算与通信重叠流水线:
echo   - 多阶段异步流水线并行
echo   - 计算与数据传输完全重叠
echo   - CPU+GPU协同调度
echo   - 适用: 大规模矩阵, 批量计算
echo.
echo 🎯 自适应算法选择框架:
echo   - 实时分析矩阵特征
echo   - 基于历史性能自动选择最优策略
echo   - 支持运行时策略切换
echo   - 适用: 所有场景的智能优化
echo.
echo 🔥 预期性能提升: 2-10倍 (相比传统方法)
echo.
pause
exit /b 0

:error
echo.
echo ❌ 编译失败！请检查:
echo   1. CUDA版本是否≥10.0
echo   2. GPU计算能力是否≥7.0
echo   3. 编译器是否支持C++11
echo   4. 是否有足够的磁盘空间
echo   5. Thrust库是否正确安装
echo.
echo 💡 故障排除建议:
echo   - 如果是Thrust相关错误，请确保CUDA完整安装
echo   - 如果是OpenMP错误，程序会自动重试不使用OpenMP
echo   - 如果是架构错误，请修改 -arch=sm_70 为您的GPU架构
echo.
pause
exit /b 1
