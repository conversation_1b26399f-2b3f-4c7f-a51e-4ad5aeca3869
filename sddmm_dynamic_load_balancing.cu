#include <iostream>
#include <vector>
#include <cuda_runtime.h>
#include <cooperative_groups.h>
#include <cub/cub.cuh>

// 动态负载均衡策略
struct WorkItem {
    int row;
    int start_nnz;
    int end_nnz;
    int work_size;
};

// 全局工作队列
__device__ int global_work_counter = 0;
__device__ WorkItem* global_work_queue;
__device__ int total_work_items;

// 动态负载均衡SDDMM内核
__global__ void sddmm_dynamic_load_balancing_kernel(
    const float* __restrict__ A, 
    const float* __restrict__ B,
    const int* __restrict__ row_ptr, 
    const int* __restrict__ col_idx,
    float* __restrict__ result,
    int M, int K, int max_work_per_block = 512) {
    
    extern __shared__ float shared_mem[];
    float* a_cache = shared_mem;
    int* work_indices = (int*)(shared_mem + K);
    
    const int tid = threadIdx.x;
    const int bid = blockIdx.x;
    const int block_size = blockDim.x;
    const int warp_id = tid / 32;
    const int lane_id = tid % 32;
    
    // 每个block动态获取工作
    while (true) {
        __shared__ int block_work_start;
        __shared__ int block_work_count;
        
        if (tid == 0) {
            block_work_start = atomicAdd(&global_work_counter, max_work_per_block);
            block_work_count = min(max_work_per_block, total_work_items - block_work_start);
        }
        __syncthreads();
        
        if (block_work_count <= 0) break;
        
        // 处理分配给当前block的工作项
        for (int work_idx = tid; work_idx < block_work_count; work_idx += block_size) {
            int global_work_idx = block_work_start + work_idx;
            if (global_work_idx >= total_work_items) break;
            
            WorkItem work = global_work_queue[global_work_idx];
            int row = work.row;
            
            // 缓存A矩阵行数据
            for (int k = lane_id; k < K; k += 32) {
                a_cache[warp_id * K + k] = A[row * K + k];
            }
            __syncwarp();
            
            // 计算该工作项的SDDMM
            for (int nnz_idx = work.start_nnz; nnz_idx < work.end_nnz; ++nnz_idx) {
                int col = col_idx[nnz_idx];
                float sum = 0.0f;
                
                #pragma unroll 4
                for (int k = lane_id; k < K; k += 32) {
                    sum += a_cache[warp_id * K + k] * B[col * K + k];
                }
                
                // Warp reduce
                #pragma unroll
                for (int offset = 16; offset > 0; offset /= 2) {
                    sum += __shfl_down_sync(0xFFFFFFFF, sum, offset);
                }
                
                if (lane_id == 0) {
                    result[nnz_idx] = sum;
                }
            }
        }
        __syncthreads();
    }
}

// 工作项生成函数
void generate_work_items(const std::vector<int>& row_ptr, 
                        std::vector<WorkItem>& work_items,
                        int adaptive_chunk_size = 64) {
    work_items.clear();
    
    for (int row = 0; row < row_ptr.size() - 1; ++row) {
        int row_start = row_ptr[row];
        int row_end = row_ptr[row + 1];
        int nnz_count = row_end - row_start;
        
        if (nnz_count == 0) continue;
        
        // 自适应分块策略
        int chunk_size = adaptive_chunk_size;
        if (nnz_count > 1024) chunk_size = 128;
        else if (nnz_count > 256) chunk_size = 64;
        else if (nnz_count > 64) chunk_size = 32;
        else chunk_size = nnz_count;
        
        for (int start = row_start; start < row_end; start += chunk_size) {
            WorkItem item;
            item.row = row;
            item.start_nnz = start;
            item.end_nnz = min(start + chunk_size, row_end);
            item.work_size = item.end_nnz - item.start_nnz;
            work_items.push_back(item);
        }
    }
    
    // 按工作量排序，大的工作项优先
    std::sort(work_items.begin(), work_items.end(), 
              [](const WorkItem& a, const WorkItem& b) {
                  return a.work_size > b.work_size;
              });
}

// 主函数接口
void sddmm_dynamic_load_balancing(
    const float* d_A, const float* d_B,
    const int* d_row_ptr, const int* d_col_idx,
    float* d_result, const std::vector<int>& h_row_ptr,
    int M, int N, int K) {
    
    // 生成工作项
    std::vector<WorkItem> h_work_items;
    generate_work_items(h_row_ptr, h_work_items);
    
    // 分配GPU内存
    WorkItem* d_work_queue;
    cudaMalloc(&d_work_queue, h_work_items.size() * sizeof(WorkItem));
    cudaMemcpy(d_work_queue, h_work_items.data(), 
               h_work_items.size() * sizeof(WorkItem), cudaMemcpyHostToDevice);
    
    // 设置全局变量
    cudaMemcpyToSymbol(global_work_queue, &d_work_queue, sizeof(WorkItem*));
    int total_items = h_work_items.size();
    cudaMemcpyToSymbol(total_work_items, &total_items, sizeof(int));
    
    // 重置工作计数器
    int zero = 0;
    cudaMemcpyToSymbol(global_work_counter, &zero, sizeof(int));
    
    // 启动内核
    int num_blocks = min(1024, (int)h_work_items.size() / 64 + 1);
    int block_size = 256;
    size_t shared_mem_size = K * sizeof(float) * (block_size / 32) + block_size * sizeof(int);
    
    sddmm_dynamic_load_balancing_kernel<<<num_blocks, block_size, shared_mem_size>>>(
        d_A, d_B, d_row_ptr, d_col_idx, d_result, M, K);
    
    cudaFree(d_work_queue);
}
