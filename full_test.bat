@echo off
echo ========================================
echo SDDMM 完整测试脚本
echo ========================================

REM 检查Python是否可用
python --version >nul 2>nul
if %errorlevel% neq 0 (
    echo 警告: 未找到Python，将跳过大矩阵生成
    goto :compile
)

echo 生成测试矩阵...
python generate_test_matrix.py 500 400 0.08 medium_test.mtx
python generate_test_matrix.py 1000 800 0.05 large_test.mtx

:compile
echo.
echo ===== 编译程序 =====
nvcc -o sddmm_test.exe sddmm_complete_executable.cu -O3 -arch=sm_70 -std=c++11

if %errorlevel% neq 0 (
    echo 编译失败!
    pause
    exit /b 1
)

echo 编译成功!
echo.

echo ===== 运行测试 =====

echo [测试1] 小矩阵测试 (验证正确性)
sddmm_test.exe test_matrix.mtx 16
echo.

if exist medium_test.mtx (
    echo [测试2] 中等矩阵测试 (性能测试)
    sddmm_test.exe medium_test.mtx 32
    echo.
)

if exist large_test.mtx (
    echo [测试3] 大矩阵测试 (性能测试)
    sddmm_test.exe large_test.mtx 64
    echo.
)

echo ===== 不同K值性能测试 =====
echo 使用小矩阵测试不同K值的性能...

for %%k in (8 16 32 64 128) do (
    echo.
    echo [K=%%k] 性能测试:
    sddmm_test.exe test_matrix.mtx %%k
)

echo.
echo ===== 所有测试完成 =====
pause
