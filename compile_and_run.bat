@echo off
echo ===== 编译SDDMM程序 =====

REM 检查CUDA是否安装
where nvcc >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到nvcc编译器，请确保CUDA已正确安装并添加到PATH
    pause
    exit /b 1
)

REM 编译程序
echo 正在编译...
nvcc -o sddmm_test.exe sddmm_complete_executable.cu -O3 -arch=sm_70 -std=c++11

if %errorlevel% neq 0 (
    echo 编译失败!
    pause
    exit /b 1
)

echo 编译成功!
echo.

echo ===== 运行测试 =====
echo 使用测试矩阵 test_matrix.mtx, K=16
echo.

REM 运行程序
sddmm_test.exe test_matrix.mtx 16

echo.
echo ===== 测试完成 =====
pause
