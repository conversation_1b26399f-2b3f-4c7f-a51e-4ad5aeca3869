#!/bin/bash

echo "===== 编译SDDMM程序 ====="

# 检查CUDA是否安装
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到nvcc编译器，请确保CUDA已正确安装并添加到PATH"
    exit 1
fi

# 编译程序
echo "正在编译..."
nvcc -o sddmm_test sddmm_complete_executable.cu -O3 -arch=sm_70 -std=c++11

if [ $? -ne 0 ]; then
    echo "编译失败!"
    exit 1
fi

echo "编译成功!"
echo

echo "===== 运行测试 ====="
echo "使用测试矩阵 test_matrix.mtx, K=16"
echo

# 运行程序
./sddmm_test test_matrix.mtx 16

echo
echo "===== 测试完成 ====="
