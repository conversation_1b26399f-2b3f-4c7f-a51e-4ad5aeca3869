# SDDMM 完整可执行程序

这是一个高度优化的SDDMM (Sampled Dense-Dense Matrix Multiplication) CUDA实现，支持稀疏矩阵的高效计算。

## 🚀 主要特性

### 性能优化
- **多级密度分类**: 根据行的非零元素数量自动分类为低/中/高密度区域
- **Warp Shuffle优化**: 使用高效的warp内归约操作
- **共享内存优化**: 减少全局内存访问，提高数据重用
- **多流并行**: 使用多个CUDA流实现并行计算
- **向量化计算**: 支持SIMD指令优化

### 内存优化
- **合并内存访问**: 优化内存访问模式提高带宽利用率
- **动态共享内存**: 根据问题规模动态分配共享内存
- **分块处理**: 支持任意大小的K维度

## 📋 系统要求

- NVIDIA GPU (计算能力 >= 7.0)
- CUDA Toolkit >= 10.0
- C++11 编译器支持
- Thrust库 (通常随CUDA安装)

## 🛠️ 编译和运行

### Windows系统
```bash
# 运行编译脚本
compile_and_run.bat
```

### Linux系统
```bash
# 给脚本执行权限
chmod +x run_sddmm_test.sh

# 运行编译和测试脚本
./run_sddmm_test.sh
```

### 手动编译
```bash
# 编译程序
nvcc -o sddmm_test sddmm_complete_executable.cu -O3 -arch=sm_70 -std=c++11

# 运行程序
./sddmm_test test_matrix.mtx 16
```

## 📊 输入格式

程序接受Matrix Market格式的稀疏矩阵文件：

```
用法: ./sddmm_test <matrix_file.mtx> <K>

参数:
- matrix_file.mtx: Matrix Market格式的稀疏矩阵文件
- K: 稠密矩阵的列数/特征维度
```

### Matrix Market格式示例
```
%%MatrixMarket matrix coordinate real general
10 8 24
1 1 1.0
1 3 2.0
...
```

## 🔧 程序输出

程序会输出详细的性能分析信息：

```
=== SDDMM 完整可执行程序 ===
矩阵文件: test_matrix.mtx
K维度: 16
矩阵维度: 10 x 8, 非零元素: 24

行分类结果: 低密度=8, 中密度=2, 高密度=0

=== GPU计算 ===
低密度区计算时间: 0.123 ms
中密度区计算时间: 0.045 ms
GPU总计算时间: 15 ms

=== CPU参考计算 ===
CPU计算时间: 125 ms

=== 结果验证 ===
验证结果: 错误数量=0/24, 最大误差=1.234567e-06
✓ 计算结果正确!
加速比: 8.33x

=== 性能统计 ===
GPU性能: 12.34 GFLOPS
```

## 📈 性能特点

### 密度分类阈值
- **低密度**: 非零元素 ≤ 32 (使用warp级并行)
- **中密度**: 32 < 非零元素 ≤ 256 (使用块级并行)  
- **高密度**: 非零元素 > 256 (使用分块处理)

### 优化策略
1. **低密度区**: 每个warp处理一行，最大化warp利用率
2. **中密度区**: 每个块处理一行，使用2D线程块布局
3. **高密度区**: 将长行分块处理，避免负载不均衡

## 🧪 测试数据

程序包含一个测试矩阵 `test_matrix.mtx`：
- 维度: 10 x 8
- 非零元素: 24个
- 适合验证程序正确性

对于大规模测试，建议使用：
- SuiteSparse Matrix Collection
- 真实应用的稀疏矩阵数据

## 🔍 故障排除

### 编译错误
1. **nvcc未找到**: 确保CUDA已正确安装并添加到PATH
2. **架构不匹配**: 修改`-arch=sm_70`为您的GPU架构
3. **内存不足**: 减小测试矩阵大小或K值

### 运行时错误
1. **共享内存超限**: 程序会自动检测并给出警告
2. **结果验证失败**: 检查输入数据格式是否正确
3. **性能异常**: 确保GPU有足够的计算资源

## 📚 算法说明

SDDMM计算公式: `S = A * B^T ⊙ S_pattern`

其中:
- A: M×K 稠密矩阵
- B: N×K 稠密矩阵  
- S_pattern: M×N 稀疏模式矩阵
- ⊙: 逐元素乘法

程序只计算稀疏模式中非零位置的结果，大幅减少计算量。

## 🎯 性能调优建议

1. **K值选择**: K=16-256通常有最佳性能
2. **矩阵格式**: 确保稀疏矩阵按行排序
3. **GPU选择**: 使用较新的GPU架构获得更好性能
4. **内存管理**: 对于大矩阵考虑分批处理

## 📄 许可证

本程序仅供学习和研究使用。
