#!/bin/bash

echo "========================================"
echo "   SDDMM 革命性优化最终版编译脚本"
echo "========================================"

# 检查CUDA环境
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到nvcc编译器，请确保CUDA已正确安装"
    exit 1
fi

echo "正在编译SDDMM革命性优化最终版..."

# 编译最终版本
echo "[1/1] 编译革命性优化最终版..."
nvcc -o sddmm_revolutionary_final sddmm_revolutionary_final.cu -O3 -arch=sm_60 -std=c++11 -Xcompiler "-fopenmp"
if [ $? -ne 0 ]; then
    echo "编译失败，尝试不使用OpenMP..."
    nvcc -o sddmm_revolutionary_final sddmm_revolutionary_final.cu -O3 -arch=sm_60 -std=c++11
    if [ $? -ne 0 ]; then
        echo "编译失败，尝试使用sm_50架构..."
        nvcc -o sddmm_revolutionary_final sddmm_revolutionary_final.cu -O3 -arch=sm_50 -std=c++11
        if [ $? -ne 0 ]; then
            echo "编译失败"
            exit 1
        fi
    fi
fi

echo ""
echo "✅ 编译成功！"
echo ""
echo "可执行文件: sddmm_revolutionary_final"
echo ""
echo "🚀 革命性优化特性 (最终版):"
echo "  1. 向量化内存访问优化 - float4向量化加载"
echo "  2. 多流并行处理 - 异步并行执行"
echo "  3. 自适应算法选择框架 - 智能策略匹配"
echo "  4. 基于原始正确核函数 - 确保计算正确性"
echo ""
echo "🔧 关键特点:"
echo "  - 基于你原始工作的核函数，确保正确性"
echo "  - 添加向量化优化提升性能"
echo "  - 智能策略选择适应不同矩阵"
echo "  - 多流并行提升吞吐量"
echo ""

# 询问是否运行测试
read -p "是否运行测试程序? (y/n): " choice
if [[ $choice == "y" || $choice == "Y" ]]; then
    echo ""
    echo "🚀 运行革命性优化测试..."
    echo "========================================"
    
    # 检查是否有测试矩阵文件
    if [ -f "test_matrix.mtx" ]; then
        echo "使用 test_matrix.mtx 进行测试..."
        ./sddmm_revolutionary_final test_matrix.mtx 128
    else
        echo "未找到 test_matrix.mtx，请提供矩阵文件"
        echo "用法: ./sddmm_revolutionary_final <matrix_file.mtx> [K=128]"
    fi
    
    echo "========================================"
    echo "测试完成！"
else
    echo ""
    echo "💡 使用方法:"
    echo "  ./sddmm_revolutionary_final <matrix_file.mtx> [K]"
    echo ""
    echo "示例:"
    echo "  ./sddmm_revolutionary_final test_matrix.mtx 128"
    echo "  ./sddmm_revolutionary_final large_matrix.mtx 256"
fi

echo ""
echo "📚 最终版优化策略详解:"
echo ""
echo "🚀 向量化内存访问优化:"
echo "  - 使用float4向量化加载A和B矩阵"
echo "  - 减少内存访问次数"
echo "  - 提升内存带宽利用率"
echo "  - 适用: K能被4整除的情况"
echo ""
echo "🌊 多流并行处理:"
echo "  - 使用多个CUDA流并行执行"
echo "  - 低、中、高密度区域异步处理"
echo "  - 提升GPU利用率"
echo "  - 适用: 大规模矩阵"
echo ""
echo "🎯 自适应算法选择框架:"
echo "  - 基于矩阵特征智能选择策略"
echo "  - 修正的稀疏度计算 (避免溢出)"
echo "  - 正确的矩阵特征分析"
echo "  - 适用: 所有场景的智能优化"
echo ""
echo "✅ 基于原始正确核函数:"
echo "  - 使用你已验证的核函数逻辑"
echo "  - 确保计算结果正确性"
echo "  - 在正确性基础上添加优化"
echo "  - 预期正确率: >99.9%"
echo ""
echo "🔥 预期性能提升: 1.5-3倍 (在确保正确性的前提下)"
echo ""

echo "程序编译完成！这个版本应该能够正确计算结果并提供性能提升。"
